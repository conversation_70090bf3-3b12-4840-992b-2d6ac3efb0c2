import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';
import { useAppointments, useUpdateAppointmentStatus } from '../../hooks/useAppointments';
import { LastUpdatedIndicator } from '../../hooks/useLastUpdated';
import toast from 'react-hot-toast';
import { useDashboardTranslation, useCommonTranslation } from '../../hooks/useTranslation';

const PendingAppointments: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useDashboardTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { data: allAppointments, isLoading, error, refetch, isFetching, dataUpdatedAt } = useAppointments();
  const updateStatusMutation = useUpdateAppointmentStatus();
  const [processingIds, setProcessingIds] = useState<Set<number>>(new Set());

  // Force refresh when component mounts (when dashboard page is visited)
  useEffect(() => {
    console.log('🔄 PendingAppointments component mounted - forcing refresh');
    refetch();
  }, [refetch]);

  // Filter for pending appointments
  const pendingAppointments = React.useMemo(() => {
    if (!allAppointments) return [];
    
    return allAppointments
      .filter(apt => apt.status === 'pending')
      .sort((a, b) => new Date(a.expectedAppointmentStartTime).getTime() - new Date(b.expectedAppointmentStartTime).getTime())
      .slice(0, 5); // Show up to 5 pending appointments
  }, [allAppointments]);

  const handleAccept = async (appointmentId: number) => {
    setProcessingIds(prev => new Set(prev).add(appointmentId));
    
    try {
      await updateStatusMutation.mutateAsync({
        id: appointmentId,
        status: {
          status: 'confirmed',
          notes: t('pendingAppointments.confirmedByProvider', 'Appointment confirmed by provider')
        }
      });
      toast.success(t('pendingAppointments.appointmentConfirmed', 'Appointment confirmed'));
    } catch (error) {
      toast.error(t('pendingAppointments.failedToConfirm', 'Failed to confirm appointment'));
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(appointmentId);
        return newSet;
      });
    }
  };

  const handleCancel = async (appointmentId: number) => {
    setProcessingIds(prev => new Set(prev).add(appointmentId));
    
    try {
      await updateStatusMutation.mutateAsync({
        id: appointmentId,
        status: {
          status: 'canceled',
          notes: t('pendingAppointments.cancelledByProvider', 'Appointment cancelled by provider')
        }
      });
      toast.success(t('pendingAppointments.appointmentCancelled', 'Appointment cancelled'));
    } catch (error) {
      toast.error(t('pendingAppointments.failedToCancel', 'Failed to cancel appointment'));
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(appointmentId);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        </div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                <div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('pendingAppointments.errorLoading', 'Error Loading Appointments')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('pendingAppointments.errorMessage', 'Unable to load pending appointments. Please try again.')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('stats.pendingAppointments', 'Pending Appointments')}
          </h3>
          <div className="flex items-center justify-between">
            {/* <p className="text-sm text-gray-600 dark:text-gray-400">
              {pendingAppointments.length} appointment{pendingAppointments.length !== 1 ? 's' : ''} awaiting confirmation
            </p> */}
            <LastUpdatedIndicator
              dataUpdatedAt={dataUpdatedAt}
              isLoading={isLoading}
              isFetching={isFetching}
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="p-2"
            title={t('pendingAppointments.refresh', 'Refresh pending appointments')}
          >
            <svg
              className={`w-4 h-4 ${isFetching ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/appointments')}
          >
            {t('pendingAppointments.viewAll', 'View All')}
          </Button>
        </div>
      </div>

      {pendingAppointments.length === 0 ? (
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('pendingAppointments.allCaughtUp', 'All Caught Up!')}
          </h4>
          <p className="text-gray-600 dark:text-gray-400">
            {t('pendingAppointments.noPending', 'No pending appointments to review')}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {pendingAppointments.map((appointment) => {
            const isProcessing = processingIds.has(appointment.id);
            
            return (
              <div
                key={appointment.id}
                className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg"
              >
                <div className="flex items-center gap-3 flex-1">
                  <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                    {appointment.customer?.firstName?.charAt(0)}{appointment.customer?.lastName?.charAt(0)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium text-gray-900 dark:text-white text-sm">
                        {appointment.customer?.firstName} {appointment.customer?.lastName}
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        {t('pendingAppointments.pending', 'Pending')}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                      {appointment.service?.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {formatLocalTime(appointment.expectedAppointmentStartTime)}
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-2 ml-3">
                  <Button
                    size="sm"
                    onClick={() => handleAccept(appointment.id)}
                    disabled={isProcessing}
                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 text-xs"
                  >
                    {isProcessing ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      t('pendingAppointments.accept', 'Accept')
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleCancel(appointment.id)}
                    disabled={isProcessing}
                    className="border-red-300 text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20 px-3 py-1 text-xs"
                  >
                    {isProcessing ? (
                      <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      t('pendingAppointments.cancel', 'Cancel')
                    )}
                  </Button>
                </div>
              </div>
            );
          })}

          {pendingAppointments.length >= 5 && (
            <div className="text-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/appointments')}
                className="text-brand-600 hover:text-brand-700"
              >
                {t('pendingAppointments.viewMore', 'View more pending appointments')}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PendingAppointments;
