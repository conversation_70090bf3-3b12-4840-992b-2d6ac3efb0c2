import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { ErrorDisplay } from '../error';
import { useCreateProviderCustomer, useUpdateProviderCustomer, useRestoreCustomer } from '../../hooks/useProviderCustomers';
import { ProviderCustomer, CustomerFormData, CustomerRestoreRequest } from '../../types/provider-customer';
import { XMarkIcon } from '../../icons';
import { useCommonTranslation, useFormTranslation, useManagementTranslation } from '../../hooks/useTranslation';

// Validation schema will be created inside component to use translations

interface ProviderCustomerFormProps {
  customer?: ProviderCustomer | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ProviderCustomerForm({ customer, onClose, onSuccess }: ProviderCustomerFormProps) {
  const isEditing = !!customer;
  const createCustomerMutation = useCreateProviderCustomer();
  const updateCustomerMutation = useUpdateProviderCustomer();
  const restoreCustomerMutation = useRestoreCustomer();
  const { t } = useCommonTranslation();
  const { t: tForm } = useFormTranslation();
  const { t: tManagement, currentLanguage } = useManagementTranslation();

  // State for handling archive error and restore flow
  const [showRestoreOption, setShowRestoreOption] = useState(false);
  const [archiveErrorDetails, setArchiveErrorDetails] = useState<CustomerRestoreRequest | null>(null);

  // Custom translations for customer form
  const formTranslations = {
    ar: {
      addNewCustomer: "إضافة عميل جديد",
      editCustomer: "تعديل العميل",
      createNewRelationship: "إنشاء علاقة عميل جديدة",
      updateCustomerInfo: "تحديث معلومات العميل وملاحظات مقدم الخدمة",
      basicInformation: "المعلومات الأساسية",
      contactInformation: "معلومات الاتصال",
      firstName: "الاسم الأول",
      lastName: "اسم العائلة",
      mobileNumber: "رقم الهاتف المحمول",
      emailAddress: "عنوان البريد الإلكتروني",
      nationalId: "رقم الهوية الوطنية",
      providerNotes: "ملاحظات مقدم الخدمة",
      firstNamePlaceholder: "أدخل الاسم الأول",
      lastNamePlaceholder: "أدخل اسم العائلة",
      mobileNumberPlaceholder: "أدخل رقم الهاتف المحمول (مثال: +213555123456)",
      emailPlaceholder: "أدخل عنوان البريد الإلكتروني (اختياري)",
      nationalIdPlaceholder: "أدخل رقم الهوية الوطنية (اختياري)",
      notesPlaceholder: "أضف أي ملاحظات حول هذا العميل (اختياري)",
      failedToSave: "فشل في حفظ العميل",
      maxCharacters: "الحد الأقصى 1000 حرف",
      createCustomer: "إنشاء عميل",
      updateCustomer: "تحديث العميل"
    },
    en: {
      addNewCustomer: "Add New Customer",
      editCustomer: "Edit Customer",
      createNewRelationship: "Create a new customer relationship",
      updateCustomerInfo: "Update customer information and provider notes",
      basicInformation: "Basic Information",
      contactInformation: "Contact Information",
      firstName: "First Name",
      lastName: "Last Name",
      mobileNumber: "Mobile Number",
      emailAddress: "Email Address",
      nationalId: "National ID",
      providerNotes: "Provider Notes",
      firstNamePlaceholder: "Enter first name",
      lastNamePlaceholder: "Enter last name",
      mobileNumberPlaceholder: "Enter mobile number (e.g., +213555123456)",
      emailPlaceholder: "Enter email address (optional)",
      nationalIdPlaceholder: "Enter national ID (optional)",
      notesPlaceholder: "Add any notes about this customer (optional)",
      failedToSave: "Failed to save customer",
      maxCharacters: "Maximum 1000 characters",
      createCustomer: "Create Customer",
      updateCustomer: "Update Customer"
    },
    fr: {
      addNewCustomer: "Ajouter un nouveau client",
      editCustomer: "Modifier le client",
      createNewRelationship: "Créer une nouvelle relation client",
      updateCustomerInfo: "Mettre à jour les informations client et les notes du prestataire",
      basicInformation: "Informations de base",
      contactInformation: "Informations de contact",
      firstName: "Prénom",
      lastName: "Nom de famille",
      mobileNumber: "Numéro de téléphone portable",
      emailAddress: "Adresse e-mail",
      nationalId: "Numéro d'identité nationale",
      providerNotes: "Notes du prestataire",
      firstNamePlaceholder: "Entrez le prénom",
      lastNamePlaceholder: "Entrez le nom de famille",
      mobileNumberPlaceholder: "Entrez le numéro de téléphone portable (ex: +213555123456)",
      emailPlaceholder: "Entrez l'adresse e-mail (optionnel)",
      nationalIdPlaceholder: "Entrez le numéro d'identité nationale (optionnel)",
      notesPlaceholder: "Ajoutez des notes sur ce client (optionnel)",
      failedToSave: "Échec de l'enregistrement du client",
      maxCharacters: "Maximum 1000 caractères",
      createCustomer: "Créer un client",
      updateCustomer: "Mettre à jour le client"
    }
  };

  const currentLang = currentLanguage as keyof typeof formTranslations;
  const ft = (key: keyof typeof formTranslations.ar) => formTranslations[currentLang]?.[key] || formTranslations.en[key] || key;

  // Validation schema using translations
  const customerSchema = z.object({
    firstName: z.string().min(1, tForm('validation.required')),
    lastName: z.string().min(1, tForm('validation.required')),
    mobileNumber: z.string().min(1, tForm('validation.required')),
    email: z.string().email(tForm('validation.emailInvalid')).optional().or(z.literal('')),
    nationalId: z.string().optional().or(z.literal('')),
    notes: z.string().max(1000, tForm('validation.maxLength', { max: 1000 })).optional().or(z.literal('')),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      mobileNumber: '',
      email: '',
      nationalId: '',
      notes: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (customer) {
      reset({
        firstName: customer.firstName,
        lastName: customer.lastName,
        mobileNumber: customer.mobileNumber,
        email: customer.email || '',
        nationalId: customer.nationalId || '',
        notes: customer.notes || '',
      });
    }
  }, [customer, reset]);

  const onSubmit = async (data: CustomerFormData) => {
    try {
      // Clean up empty strings to undefined for optional fields
      const cleanData = {
        ...data,
        email: data.email?.trim() || undefined,
        nationalId: data.nationalId?.trim() || undefined,
        notes: data.notes?.trim() || undefined,
      };

      if (isEditing && customer) {
        await updateCustomerMutation.mutateAsync({
          customerUserId: customer.id,
          ...cleanData,
        });
      } else {
        await createCustomerMutation.mutateAsync(cleanData);
      }

      onSuccess();
    } catch (error: any) {
      // Check if this is an archive error
      const errorMessage = error?.response?.data?.message || error?.message || '';
      if (errorMessage.includes('Customer exists in archive')) {
        // Extract restore data from the form
        const restoreData: CustomerRestoreRequest = {};
        if (cleanData.email) restoreData.email = cleanData.email;
        if (cleanData.mobileNumber) restoreData.mobileNumber = cleanData.mobileNumber;
        if (cleanData.nationalId) restoreData.nationalId = cleanData.nationalId;

        setArchiveErrorDetails(restoreData);
        setShowRestoreOption(true);
      }
      // Other errors are handled by mutation hooks
    }
  };

  const handleRestoreCustomer = async () => {
    if (!archiveErrorDetails) return;

    try {
      await restoreCustomerMutation.mutateAsync(archiveErrorDetails);
      setShowRestoreOption(false);
      setArchiveErrorDetails(null);
      onSuccess();
    } catch (error) {
      // Error handled by mutation hook
    }
  };

  const handleCancelRestore = () => {
    setShowRestoreOption(false);
    setArchiveErrorDetails(null);
  };

  const isLoading = createCustomerMutation.isPending || updateCustomerMutation.isPending || restoreCustomerMutation.isPending;
  const error = createCustomerMutation.error || updateCustomerMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl w-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? ft('editCustomer') : ft('addNewCustomer')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing
              ? ft('updateCustomerInfo')
              : ft('createNewRelationship')
            }
          </p>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <XMarkIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Error Display */}
        {error && (
          <ErrorDisplay
            error={error}
            title={ft('failedToSave')}
            variant="inline"
          />
        )}

        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {ft('basicInformation')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" required>
                {ft('firstName')}
              </Label>
              <Input
                id="firstName"
                type="text"
                placeholder={ft('firstNamePlaceholder')}
                error={errors.firstName?.message}
                {...register('firstName')}
              />
            </div>

            <div>
              <Label htmlFor="lastName" required>
                {ft('lastName')}
              </Label>
              <Input
                id="lastName"
                type="text"
                placeholder={ft('lastNamePlaceholder')}
                error={errors.lastName?.message}
                {...register('lastName')}
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {ft('contactInformation')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="mobileNumber" required>
                {ft('mobileNumber')}
              </Label>
              <Input
                id="mobileNumber"
                type="tel"
                placeholder={ft('mobileNumberPlaceholder')}
                error={errors.mobileNumber?.message}
                {...register('mobileNumber')}
              />
            </div>

            <div>
              <Label htmlFor="email">
                {ft('emailAddress')}
              </Label>
              <Input
                id="email"
                type="email"
                placeholder={ft('emailPlaceholder')}
                error={errors.email?.message}
                {...register('email')}
              />
            </div>

            <div>
              <Label htmlFor="nationalId">
                {ft('nationalId')}
              </Label>
              <Input
                id="nationalId"
                type="text"
                placeholder={ft('nationalIdPlaceholder')}
                error={errors.nationalId?.message}
                {...register('nationalId')}
              />
            </div>
          </div>
        </div>

        {/* Provider Notes */}
        <div>
          <Label htmlFor="notes">
            {ft('providerNotes')}
          </Label>
          <textarea
            id="notes"
            rows={4}
            placeholder={ft('notesPlaceholder')}
            className="w-full rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
            {...register('notes')}
          />
          {errors.notes && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.notes.message}
            </p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {ft('maxCharacters')}
          </p>
        </div>

        {/* Restore Option */}
        {showRestoreOption && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {tManagement('customers.restore.customerExistsInArchive')}
                </h3>
                <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                  {tManagement('customers.restore.customerExistsMessage')}
                </p>
                <div className="mt-3 flex space-x-3">
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleRestoreCustomer}
                    disabled={restoreCustomerMutation.isPending}
                    loading={restoreCustomerMutation.isPending}
                  >
                    {tManagement('customers.restore.restoreButton')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleCancelRestore}
                    disabled={restoreCustomerMutation.isPending}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {t('actions.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={isLoading || showRestoreOption}
            loading={isLoading}
          >
            {isEditing ? ft('updateCustomer') : ft('createCustomer')}
          </Button>
        </div>
      </form>
    </div>
  );
}
