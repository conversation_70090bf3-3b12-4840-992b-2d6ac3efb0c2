/**
 * Provider Customer Types
 * Based on the provider-customers-docs.md documentation
 */

import { User, Appointment } from './index';

/**
 * Customer Folder - Provider-Customer Relationship Model
 */
export interface CustomerFolder {
  id: number;
  createdAt: string;
  updatedAt: string;
  sProviderId: number;
  userId: string;
  notes?: string;
  isActive: boolean;
  appointments?: Appointment[];
}

/**
 * Provider Customer - Complete customer data with provider context
 */
export interface ProviderCustomer {
  id: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
  appointmentCount: number;
  createdAt: string;
  updatedAt?: string;
  isActive?: boolean;
  
  // Customer folder relationship data
  folder?: CustomerFolder;
  
  // User profile data
  username?: string;
  role?: string;
  isEmailVerified?: boolean;
  profilePictureId?: string;
}

/**
 * Customer creation request
 */
export interface ProviderCustomerCreateRequest {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

/**
 * Customer update request
 */
export interface ProviderCustomerUpdateRequest {
  customerUserId: string;
  firstName?: string;
  lastName?: string;
  mobileNumber?: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

/**
 * Customer filters for search and pagination
 */
export interface ProviderCustomerFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'email' | 'createdAt' | 'appointmentCount' | 'lastAppointment';
  sortOrder?: 'asc' | 'desc';
  isActive?: boolean;
  hasEmail?: boolean;
  hasNationalId?: boolean;
  appointmentCount?: {
    min?: number;
    max?: number;
  };
  createdAfter?: string;
  createdBefore?: string;
  lastAppointmentAfter?: string;
}

/**
 * Paginated customer response
 */
export interface ProviderCustomerResponse {
  customers: ProviderCustomer[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  searchQuery?: string;
}

/**
 * Customer folder details with full appointment history
 */
export interface CustomerFolderDetails extends CustomerFolder {
  customer: {
    id: string;
    firstName: string;
    lastName: string;
    mobileNumber: string;
    email?: string;
    nationalId?: string;
    createdAt: string;
  };
  appointments: Array<{
    id: number;
    expectedAppointmentStartTime: string;
    expectedAppointmentEndTime: string;
    status: string;
    notes?: string;
    service?: {
      id: number;
      title: string;
      duration: number;
    };
    place?: {
      id: number;
      name: string;
    };
    queue?: {
      id: number;
      title: string;
    };
  }>;
  appointmentStats: {
    total: number;
    completed: number;
    cancelled: number;
    upcoming: number;
  };
}

/**
 * Customer communication preferences
 */
export interface CustomerCommunicationPreferences {
  appointmentReminders: {
    enabled: boolean;
    timing: '24h' | '2h' | '30min' | 'all';
    method: 'sms' | 'email' | 'push' | 'all';
  };
  appointmentUpdates: {
    enabled: boolean;
    method: 'sms' | 'email' | 'push' | 'all';
  };
  promotionalMessages: {
    enabled: boolean;
    method: 'sms' | 'email' | 'push';
  };
  queueUpdates: {
    enabled: boolean;
    method: 'push' | 'sms';
  };
}

/**
 * Customer data export format
 */
export interface CustomerDataExport {
  exportDate: string;
  customer: {
    id: string;
    firstName: string;
    lastName: string;
    mobileNumber: string;
    email?: string;
    nationalId?: string;
    createdAt: string;
  };
  providerNotes?: string;
  relationshipCreated: string;
  appointments: Array<{
    id: number;
    service: string;
    location: string;
    queue?: string;
    scheduledTime: string;
    status: string;
    notes?: string;
  }>;
}

/**
 * Customer validation schema types
 */
export interface CustomerValidationErrors {
  firstName?: string;
  lastName?: string;
  mobileNumber?: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

/**
 * Customer form data for React Hook Form
 */
export interface CustomerFormData {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

/**
 * Customer restore request - can restore by email, mobileNumber, or nationalId
 */
export interface CustomerRestoreRequest {
  email?: string;
  mobileNumber?: string;
  nationalId?: string;
}

/**
 * Customer restore response
 */
export interface CustomerRestoreResponse {
  success: boolean;
  message: string;
  customer?: ProviderCustomer;
}

/**
 * Soft delete response
 */
export interface CustomerSoftDeleteResponse {
  success: boolean;
  message: string;
}

/**
 * Archive error details - when trying to create a customer that exists in archive
 */
export interface CustomerArchiveError {
  code: 'CUSTOMER_EXISTS_IN_ARCHIVE';
  message: string;
  details: {
    email?: string;
    mobileNumber?: string;
    nationalId?: string;
  };
}
