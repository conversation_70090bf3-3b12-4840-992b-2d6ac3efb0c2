# Dynamic Currency System

## Overview

This document describes the implementation of a dynamic currency system in the Dalti Provider Dashboard. The system replaces all hardcoded "$" currency symbols with a configurable currency state variable that supports multiple currencies (USD, DZD, EUR, CAD).

## Problem Statement

The application previously had hardcoded "$" symbols throughout the codebase in:
- Service pricing displays
- Revenue charts and dashboard metrics
- Appointment cost displays
- Subscription plan pricing
- Various UI components showing monetary values

This made the application unsuitable for international markets and different currency preferences.

## Solution Architecture

### 1. Core Currency Context (`src/context/CurrencyContext.tsx`)

The `CurrencyContext` provides centralized currency management:

```typescript
interface CurrencyContextType {
  defaultCurrency: SupportedCurrency;
  currencySymbol: string;
  setDefaultCurrency: (currency: SupportedCurrency) => void;
  formatPrice: (amount: number, options?: FormatOptions) => string;
  isLoading: boolean;
}
```

**Key Features:**
- **Automatic Detection**: Detects currency from browser locale and user location
- **Persistent Storage**: Saves preference to localStorage
- **Event System**: Dispatches `currencyChanged` events for component synchronization
- **Future-Ready**: Prepared for backend user preference synchronization

### 2. Currency Utilities (`src/utils/currency.utils.ts`)

Comprehensive currency formatting utilities:

```typescript
// Supported currencies with full configuration
type SupportedCurrency = 'DZD' | 'EUR';

// Format currency with proper localization
formatCurrency(amount: number, currency: SupportedCurrency, options?: FormatOptions): string

// Get currency symbol and name
getCurrencySymbol(currency: SupportedCurrency): string
getCurrencyName(currency: SupportedCurrency): string
```

**Currency Configurations:**
- **DZD**: د.ج (after, with space) - Algerian Dinar (no decimals)
- **EUR**: € (after, with space) - Euro

### 3. Subscription Currency Utilities (`src/utils/subscription-currency.utils.ts`)

Handles subscription plan price conversion:

```typescript
// Extract numeric price from formatted strings
extractPriceFromString(priceString: string): number | null

// Convert subscription prices between currencies
formatSubscriptionPrice(originalPrice: string, targetCurrency: SupportedCurrency): string

// Format entire plan objects with new currency
formatPlanWithCurrency(plan: SubscriptionPlan, targetCurrency: SupportedCurrency): SubscriptionPlan
```

### 4. Currency Selector Component (`src/components/ui/CurrencySelector.tsx`)

User interface for currency selection (matches LanguageSwitcher design exactly):

```typescript
interface CurrencySelectorProps {
  variant?: 'header' | 'sidebar' | 'standalone';
  showLabel?: boolean;
  className?: string;
  disabled?: boolean;
}
```

**Features:**
- **Three Variants**: Header (circular), sidebar (full width), standalone (bordered)
- **Consistent Design**: Matches LanguageSwitcher styling exactly
- **Visual Indicators**: Country flags, currency symbols, and names
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Loading States**: Shows loading indicator during currency changes
- **Click Outside**: Closes dropdown when clicking outside

**Variants:**
- **CompactCurrencySelector**: For headers (circular button with country flag)
- **SidebarCurrencySelector**: For sidebars (full width with labels)
- **CurrencySelector**: Standalone with full customization

**Flag Mapping** (distinct from language flags to avoid confusion):
- **DZD**: 🇩🇿 (Algeria flag) - Different from Arabic language flag 🇸🇦
- **EUR**: 🇪🇺 (European Union flag) - Different from French language flag 🇫🇷

## Implementation Details

### Provider Hierarchy

The currency system is integrated into the main provider hierarchy:

```typescript
<I18nextProvider i18n={i18n}>
  <QueryClientProvider client={queryClient}>
    <LanguageProvider>
      <ThemeProvider>
        <AuthProvider>
          <CurrencyProvider>  // ← Currency context added here
            {/* App components */}
          </CurrencyProvider>
        </AuthProvider>
      </ThemeProvider>
    </LanguageProvider>
  </QueryClientProvider>
</I18nextProvider>
```

### Component Updates

All components with hardcoded "$" symbols have been updated:

#### 1. ServiceCard Component
```typescript
// Before
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
};

// After
const { formatPrice } = useCurrency();
// Usage: {formatPrice(service.price)}
```

#### 2. RevenueChart Component
```typescript
// Before
<p>${totalRevenue.toLocaleString()} total revenue</p>

// After
const { formatPrice } = useCurrency();
<p>{formatPrice(totalRevenue)} total revenue</p>
```

#### 3. AppointmentDetails Component
```typescript
// Before
<span>${appointment.service?.price}</span>

// After
const { formatPrice } = useCurrency();
<span>{formatPrice(appointment.service?.price || 0)}</span>
```

### Header Integration

The currency selector is integrated into the main application header:

```typescript
// In AppHeader.tsx
<CompactLanguageSwitcher />
<CurrencySelector compact />  // ← Added here
<NotificationCenter />
```

## Usage Examples

### Basic Usage (Automatic)

Most components get automatic currency formatting:

```typescript
const MyComponent = () => {
  const { formatPrice } = useCurrency();
  
  return (
    <div>
      <p>Service Price: {formatPrice(99.99)}</p>
      <p>Total Revenue: {formatPrice(1234.56)}</p>
    </div>
  );
};
```

### Advanced Formatting Options

```typescript
const { formatPrice } = useCurrency();

// Basic formatting
formatPrice(99.99) // "$99.99" or "99,99 €" or "100 د.ج"

// With options
formatPrice(99.99, { 
  showSymbol: false,  // "99.99"
  showCode: true,     // "$99.99 USD"
  compact: true       // "$100"
})
```

### Subscription Price Conversion

```typescript
import { formatSubscriptionPrice } from '../utils/subscription-currency.utils';

const convertedPrice = formatSubscriptionPrice("$9.99", "DZD");
// Result: "1340 د.ج" (approximate conversion)
```

### Currency Change Listeners

```typescript
import { useCurrencyChangeListener } from '../context/CurrencyContext';

const MyComponent = () => {
  useCurrencyChangeListener((currency, symbol) => {
    console.log(`Currency changed to ${currency} (${symbol})`);
    // Custom logic when currency changes
  });
  
  return <div>...</div>;
};
```

## Testing

### Test Page

A comprehensive test page is available at `/test-currency` that provides:

- **Currency Selector Testing**: Both full and compact selectors
- **Price Formatting Tests**: Test different amounts with all currencies
- **Subscription Conversion Tests**: Test plan price conversions
- **Price Extraction Tests**: Test parsing of various price formats
- **Real-time Results**: Shows conversion results and test outcomes

### Manual Testing

1. **Navigate to test page**: `/test-currency`
2. **Change currency**: Use header selector or test page selectors
3. **Verify formatting**: Check that all prices update correctly
4. **Test edge cases**: Try free plans, large amounts, decimal handling

### Automated Testing

The implementation includes console logging for debugging:

```typescript
// Currency changes are logged
console.log(`💰 Currency changed to ${currency} (${symbol})`);

// Price conversions are logged
console.log(`Converting ${originalPrice} to ${targetCurrency}: ${convertedPrice}`);
```

## Configuration

### Default Currency Detection

The system automatically detects the appropriate currency:

1. **localStorage**: Checks for saved preference
2. **Browser Locale**: Detects from `navigator.language`
3. **Business Location**: Uses provider's business address (future)
4. **Fallback**: Defaults to USD

### Exchange Rates

Currently uses hardcoded exchange rates for demonstration:

```typescript
const defaultExchangeRates = {
  USD: { EUR: 0.85, DZD: 134, CAD: 1.25 },
  EUR: { USD: 1.18, DZD: 158, CAD: 1.47 },
  DZD: { USD: 0.0075, EUR: 0.0063, CAD: 0.0094 },
  CAD: { USD: 0.80, EUR: 0.68, DZD: 107 },
};
```

**Future Enhancement**: Integration with real-time exchange rate APIs.

### Currency-Specific Settings

Each currency has specific formatting rules:

```typescript
DZD: {
  decimals: 0,           // No decimal places
  symbolPosition: 'after', // Symbol after amount
  spaceBetween: true,    // Space between amount and symbol
}

USD: {
  decimals: 2,           // Two decimal places
  symbolPosition: 'before', // Symbol before amount
  spaceBetween: false,   // No space between symbol and amount
}
```

## Best Practices

### 1. Use Context Hook

Always use the `useCurrency` hook instead of hardcoding currency:

```typescript
// ✅ Good
const { formatPrice } = useCurrency();
return <span>{formatPrice(amount)}</span>;

// ❌ Bad
return <span>${amount}</span>;
```

### 2. Handle Edge Cases

Always handle null/undefined amounts:

```typescript
// ✅ Good
{formatPrice(service?.price || 0)}

// ❌ Bad
{formatPrice(service.price)} // Could crash if price is undefined
```

### 3. Respect Currency Rules

Use appropriate decimal places for each currency:

```typescript
// DZD typically doesn't use decimals
formatPrice(1500.50, 'DZD') // "1501 د.ج"

// USD uses 2 decimal places
formatPrice(15.5, 'USD') // "$15.50"
```

### 4. Test Conversions

Always test subscription price conversions:

```typescript
// Test that conversions make sense
const converted = formatSubscriptionPrice("$9.99", "DZD");
console.log(`$9.99 → ${converted}`); // Should be reasonable amount
```

## Future Enhancements

### 1. Backend Integration

- **User Preferences**: Store currency preference in user profile
- **Real-time Rates**: Fetch exchange rates from financial APIs
- **Regional Pricing**: Different pricing tiers for different currencies

### 2. Advanced Features

- **Currency History**: Track currency changes for analytics
- **Smart Conversion**: Suggest optimal currency based on location
- **Multi-currency Display**: Show prices in multiple currencies simultaneously

### 3. Business Features

- **Revenue Analytics**: Currency-aware revenue reporting
- **Payment Integration**: Currency-specific payment methods
- **Tax Calculations**: Currency-aware tax calculations

## Troubleshooting

### Common Issues

1. **Prices Not Updating**: Check if component is using `useCurrency` hook
2. **Conversion Errors**: Verify exchange rates and price extraction logic
3. **Formatting Issues**: Check currency configuration for decimal places
4. **Performance**: Monitor for excessive re-renders during currency changes

### Debug Tools

1. **Console Logs**: Check browser console for currency change logs
2. **Test Page**: Use `/test-currency` for comprehensive testing
3. **React DevTools**: Inspect CurrencyContext state
4. **Network Tab**: Monitor for any API calls during currency changes

The currency system provides a robust foundation for international expansion while maintaining backward compatibility and excellent user experience.
