import React, { useEffect, useState } from 'react';
import { useCommonTranslation } from './useTranslation';

/**
 * Hook to track and format last updated timestamp for data
 */
export const useLastUpdated = (dataUpdatedAt?: number) => {
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [timeAgo, setTimeAgo] = useState<string>('');

  useEffect(() => {
    if (!dataUpdatedAt) return;

    const updateTimestamp = () => {
      const now = new Date();
      const updated = new Date(dataUpdatedAt);
      const diffInSeconds = Math.floor((now.getTime() - updated.getTime()) / 1000);

      // Format last updated time
      setLastUpdated(updated.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
      }));

      // Format time ago
      if (diffInSeconds < 60) {
        setTimeAgo(`${diffInSeconds}s ago`);
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        setTimeAgo(`${minutes}m ago`);
      } else {
        const hours = Math.floor(diffInSeconds / 3600);
        setTimeAgo(`${hours}h ago`);
      }
    };

    updateTimestamp();
    const interval = setInterval(updateTimestamp, 1000);

    return () => clearInterval(interval);
  }, [dataUpdatedAt]);

  return { lastUpdated, timeAgo };
};

/**
 * Component to display last updated information
 */
interface LastUpdatedIndicatorProps {
  dataUpdatedAt?: number;
  isLoading?: boolean;
  isFetching?: boolean;
  className?: string;
}

export const LastUpdatedIndicator: React.FC<LastUpdatedIndicatorProps> = ({
  dataUpdatedAt,
  isLoading,
  isFetching,
  className = ''
}) => {
  const { timeAgo } = useLastUpdated(dataUpdatedAt);
  const { t } = useCommonTranslation();

  if (isLoading) {
    return (
      <div className={`flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 ${className}`}>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
        <span>{t('messages.loading')}</span>
      </div>
    );
  }

  if (isFetching) {
    return (
      <div className={`flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 ${className}`}>
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        <span>{t('messages.updating')}</span>
      </div>
    );
  }

  if (!dataUpdatedAt) {
    return null;
  }

  return (
    <div className={`flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 ${className}`}>
      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
      <span>{t('messages.updated', 'Updated')} {timeAgo}</span>
    </div>
  );
};
