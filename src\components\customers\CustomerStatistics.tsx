import React from 'react';
import { ProviderCustomer } from '../../types/provider-customer';
import { useManagementTranslation } from '../../hooks/useTranslation';
import {
  UserGroupIcon,
  ChartBarIcon,
  CalendarIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '../../icons';

interface CustomerStatisticsProps {
  customers: ProviderCustomer[];
  className?: string;
}

interface StatCard {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

export default function CustomerStatistics({ customers, className = '' }: CustomerStatisticsProps) {
  const { t } = useManagementTranslation();

  // Ensure customers is always an array for safe operations
  const safeCustomers = Array.isArray(customers) ? customers : [];

  // Calculate statistics
  const totalCustomers = safeCustomers.length;
  const activeCustomers = safeCustomers.filter(c => c.isActive !== false).length;
  const inactiveCustomers = totalCustomers - activeCustomers;
  const totalAppointments = safeCustomers.reduce((sum, c) => sum + c.appointmentCount, 0);
  const avgAppointmentsPerCustomer = totalCustomers > 0 ? Math.round(totalAppointments / totalCustomers) : 0;

  // Calculate new customers this month
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const newCustomersThisMonth = safeCustomers.filter(c => {
    const createdDate = new Date(c.createdAt);
    return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
  }).length;

  // Calculate customer retention rate (simplified)
  const customersWithMultipleAppointments = safeCustomers.filter(c => c.appointmentCount > 1).length;
  const retentionRate = totalCustomers > 0 ? Math.round((customersWithMultipleAppointments / totalCustomers) * 100) : 0;

  // Mock change data (in a real app, this would come from historical data)
  const stats: StatCard[] = [
    {
      title: t('customers.totalCustomers'),
      value: totalCustomers,
      change: {
        value: 12,
        type: 'increase',
        period: t('customers.vsLastMonth')
      },
      icon: UserGroupIcon,
      color: 'blue'
    },
    {
      title: t('customers.activeCustomers'),
      value: activeCustomers,
      change: {
        value: 8,
        type: 'increase',
        period: t('customers.vsLastMonth')
      },
      icon: ChartBarIcon,
      color: 'green'
    },
    {
      title: t('customers.newThisMonth'),
      value: newCustomersThisMonth,
      change: {
        value: 15,
        type: 'increase',
        period: t('customers.vsLastMonth')
      },
      icon: CalendarIcon,
      color: 'purple'
    },
    {
      title: t('customers.avgAppointments'),
      value: avgAppointmentsPerCustomer,
      change: {
        value: 5,
        type: 'decrease',
        period: t('customers.vsLastMonth')
      },
      icon: ClockIcon,
      color: 'orange'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-50 dark:bg-blue-900/20',
        icon: 'text-blue-600 dark:text-blue-400',
        text: 'text-blue-600 dark:text-blue-400'
      },
      green: {
        bg: 'bg-green-50 dark:bg-green-900/20',
        icon: 'text-green-600 dark:text-green-400',
        text: 'text-green-600 dark:text-green-400'
      },
      purple: {
        bg: 'bg-purple-50 dark:bg-purple-900/20',
        icon: 'text-purple-600 dark:text-purple-400',
        text: 'text-purple-600 dark:text-purple-400'
      },
      orange: {
        bg: 'bg-orange-50 dark:bg-orange-900/20',
        icon: 'text-orange-600 dark:text-orange-400',
        text: 'text-orange-600 dark:text-orange-400'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const colors = getColorClasses(stat.color);
          const Icon = stat.icon;
          
          return (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  {stat.change && (
                    <div className="flex items-center mt-2">
                      {stat.change.type === 'increase' ? (
                        <ArrowUpIcon className="w-4 h-4 text-green-500 mr-1" />
                      ) : (
                        <ArrowDownIcon className="w-4 h-4 text-red-500 mr-1" />
                      )}
                      <span className={`text-sm font-medium ${
                        stat.change.type === 'increase' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {stat.change.value}%
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                        {stat.change.period}
                      </span>
                    </div>
                  )}
                </div>
                <div className={`w-12 h-12 ${colors.bg} rounded-lg flex items-center justify-center`}>
                  <Icon className={`w-6 h-6 ${colors.icon}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Distribution */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('customers.customerDistribution')}
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.activeCustomers')}</span>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {activeCustomers}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  ({totalCustomers > 0 ? Math.round((activeCustomers / totalCustomers) * 100) : 0}%)
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.inactiveCustomers')}</span>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {inactiveCustomers}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  ({totalCustomers > 0 ? Math.round((inactiveCustomers / totalCustomers) * 100) : 0}%)
                </span>
              </div>
            </div>

            {/* Visual Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Engagement */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('customers.customerEngagement')}
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.totalAppointments')}</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {totalAppointments}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.avgPerCustomer')}</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {avgAppointmentsPerCustomer}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.retentionRate')}</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {retentionRate}%
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('customers.repeatCustomers')}</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {customersWithMultipleAppointments}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
