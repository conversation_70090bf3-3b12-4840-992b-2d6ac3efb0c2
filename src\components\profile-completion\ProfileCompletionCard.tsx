/**
 * ProfileCompletionCard Component
 * Main UI component for displaying profile completion status
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import {
  ProfileCompletionCardProps,
  CompletionSection,
  CompletionAction,
} from '../../types/profile-completion';
import { ProfileCompletionService } from '../../services/profile-completion.service';
import { calculateProfileCompletion } from '../../utils/profile-completion';
import { useDashboardTranslation, useCommonTranslation } from '../../hooks/useTranslation';

const ProfileCompletionCard: React.FC<ProfileCompletionCardProps> = ({
  data,
  showDetails = false,
  onActionClick,
  onDismiss,
  className,
}) => {
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(showDetails);
  const { t } = useDashboardTranslation();
  const { t: tCommon } = useCommonTranslation();

  // Use service methods directly instead of hooks
  const getNavigationRoute = (section: string) => ProfileCompletionService.getNavigationRoute(section);
  const getSectionDisplayName = (section: string) => ProfileCompletionService.getSectionDisplayName(section);
  const getCompletionColor = (percentage: number) => ProfileCompletionService.getCompletionColor(percentage);
  const getCompletionStatusText = (percentage: number) => ProfileCompletionService.getCompletionStatusText(percentage);
  const formatPercentage = (percentage: number) => ProfileCompletionService.formatPercentage(percentage);
  const isFunctionallyComplete = (percentage: number) => ProfileCompletionService.isFunctionallyComplete(percentage);

  // Calculate completion from the provided data
  const completion = data ? calculateProfileCompletion(data) : null;

  if (!completion) {
    return null;
  }

  const handleSectionAction = (section: CompletionSection, action: CompletionAction) => {
    if (onActionClick) {
      onActionClick(section, action);
    } else {
      const route = getNavigationRoute(section);
      if (route) {
        navigate(route);
      }
    }
  };

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
  };

  const getProgressBarColor = (percentage: number) => {
    const color = getCompletionColor(percentage);
    const colorMap = {
      green: 'bg-green-500',
      blue: 'bg-blue-500',
      yellow: 'bg-yellow-500',
      orange: 'bg-orange-500',
      red: 'bg-red-500',
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-300';
  };

  const getSectionIcon = (section: CompletionSection) => {
    const icons = {
      profilePicture: '🖼️',
      providerInfo: '📋',
      providingPlaces: '📍',
      services: '🛠️',
      queues: '📅',
    };
    return icons[section] || '📄';
  };

  return (
    <div className={clsx(
      'bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📊</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {t('profileCompletion.title', 'Profile Setup Progress')}
            </h3>
            <p className="text-sm text-gray-600">
              {getCompletionStatusText(completion.overallPercentage)} • {formatPercentage(completion.overallPercentage)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label={isExpanded ? t('profileCompletion.collapse', 'Collapse details') : t('profileCompletion.expand', 'Expand details')}
          >
            <svg
              className={clsx('w-5 h-5 transition-transform', {
                'rotate-180': isExpanded,
              })}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {onDismiss && (
            <button
              onClick={handleDismiss}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Dismiss"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">{t('profileCompletion.overallProgress', 'Overall Progress')}</span>
          <span className="text-sm font-semibold text-gray-900">
            {formatPercentage(completion.overallPercentage)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className={clsx(
              'h-3 rounded-full transition-all duration-300',
              getProgressBarColor(completion.overallPercentage)
            )}
            style={{ width: `${completion.overallPercentage}%` }}
          />
        </div>
      </div>

      {/* Status Message */}
      {isFunctionallyComplete(completion.overallPercentage) ? (
        <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
          <div className="flex items-center">
            <span className="text-green-500 mr-2">✅</span>
            <span className="text-green-800 text-sm font-medium">
              Your profile is ready to accept appointments!
            </span>
          </div>
        </div>
      ) : (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
          <div className="flex items-center">
            <span className="text-blue-500 mr-2">ℹ️</span>
            <span className="text-blue-800 text-sm">
              Complete your profile to start accepting appointments
            </span>
          </div>
        </div>
      )}

      {/* Next Steps */}
      {completion.nextSteps.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">{t('profileCompletion.nextSteps', 'Next Steps')}:</h4>
          <ul className="space-y-1">
            {completion.nextSteps.slice(0, 2).map((step, index) => (
              <li key={index} className="text-sm text-gray-600 flex items-center">
                <span className="text-blue-500 mr-2">•</span>
                {step}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Detailed Breakdown */}
      {isExpanded && (
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Section Details:</h4>
          <div className="space-y-3">
            {Object.entries(completion.breakdown).map(([sectionKey, section]) => {
              const sectionName = getSectionDisplayName(sectionKey);
              const icon = getSectionIcon(sectionKey as CompletionSection);
              
              return (
                <div key={sectionKey} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{icon}</span>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900">{sectionName}</span>
                        {section.completed ? (
                          <span className="text-green-500 text-xs">✓</span>
                        ) : (
                          <span className="text-red-500 text-xs">✗</span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600">{section.details}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-700">
                      {formatPercentage(section.percentage)}
                    </span>
                    {!section.completed && (
                      <button
                        onClick={() => handleSectionAction(sectionKey as CompletionSection, 'complete')}
                        className="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Complete
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
        >
          {isExpanded ? t('profileCompletion.showLess', 'Show Less') : t('profileCompletion.viewDetails', 'View Details')}
        </button>
        
        {!isFunctionallyComplete(completion.overallPercentage) && (
          <button
            onClick={() => {
              const firstIncompleteSection = Object.entries(completion.breakdown)
                .find(([_, section]) => !section.completed)?.[0];
              if (firstIncompleteSection) {
                handleSectionAction(firstIncompleteSection as CompletionSection, 'complete');
              }
            }}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            {t('profileCompletion.continueSetup', 'Continue Setup')}
          </button>
        )}
      </div>
    </div>
  );
};

export default ProfileCompletionCard;
