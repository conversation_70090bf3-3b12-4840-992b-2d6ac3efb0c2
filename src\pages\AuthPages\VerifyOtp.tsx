import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import PageMeta from '../../components/common/PageMeta';
import AuthLayout from './AuthPageLayout';
import OtpInput from '../../components/auth/OtpInput';
import Button from '../../components/ui/button/Button';
import { ErrorDisplay } from '../../components/error';
import { useVerifyOtpRegister, useRequestEmailOtp, useRequestPhoneOtp } from '../../hooks/useAuthMutations';
import { useAuth } from '../../context/AuthContext';
import { VerifyOtpRegisterRequest } from '../../types';
import { useAuthTranslation } from '../../hooks/useTranslation';

// Validation schema
const otpSchema = z.object({
  otp: z.string().min(6, 'OTP must be 6 digits').max(6, 'OTP must be 6 digits'),
});

type OtpFormData = z.infer<typeof otpSchema>;

interface LocationState {
  registrationData?: {
    identifier: string;
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    providerCategoryId: number;
    businessName: string;
    phone: string;
    verificationType: 'email' | 'phone';
  };
}

export default function VerifyOtp() {
  const navigate = useNavigate();
  const location = useLocation();
  const { checkAuthStatus } = useAuth();

  const [otp, setOtp] = useState('');
  const { t } = useAuthTranslation();
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);

  const state = location.state as LocationState;
  const registrationData = state?.registrationData;

  // Redirect if no registration data
  useEffect(() => {
    if (!registrationData) {
      navigate('/signup', { replace: true });
    }
  }, [registrationData, navigate]);

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<OtpFormData>({
    resolver: zodResolver(otpSchema),
  });

  const verifyOtpMutation = useVerifyOtpRegister();
  const requestEmailOtpMutation = useRequestEmailOtp();
  const requestPhoneOtpMutation = useRequestPhoneOtp();

  const handleOtpChange = (value: string) => {
    setOtp(value);
    setValue('otp', value);
  };

  const handleOtpComplete = (value: string) => {
    setValue('otp', value);
  };

  const onSubmit = async (data: OtpFormData) => {
    if (!registrationData) return;



    const verifyData: VerifyOtpRegisterRequest = {
      identifier: registrationData.identifier,
      otp: data.otp,
      password: registrationData.password,
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      providerCategoryId: registrationData.providerCategoryId,
      businessName: registrationData.businessName,
      phone: registrationData.phone,
    };

    console.log('Verify Data being sent to API:', verifyData);

    try {
      await verifyOtpMutation.mutateAsync(verifyData);
      await checkAuthStatus();
      navigate('/', { replace: true });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleResendOtp = async () => {
    if (!registrationData || !canResend) return;

    const otpData = {
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      password: registrationData.password,
      isProviderRegistration: true,
      providerCategoryId: registrationData.providerCategoryId,
      businessName: registrationData.businessName,
      phone: registrationData.phone,
      ...(registrationData.verificationType === 'email'
        ? { email: registrationData.identifier }
        : { phoneNumber: registrationData.identifier }
      ),
    };

    try {
      if (registrationData.verificationType === 'email') {
        await requestEmailOtpMutation.mutateAsync(otpData);
      } else {
        await requestPhoneOtpMutation.mutateAsync(otpData);
      }
      
      setTimeLeft(300);
      setCanResend(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!registrationData) {
    return null; // Will redirect
  }

  const isLoading = verifyOtpMutation.isPending || requestEmailOtpMutation.isPending || requestPhoneOtpMutation.isPending;

  return (
    <>
      <PageMeta
        title={`${t('verification.title')} | Dalti Provider Dashboard`}
        description="Verify your OTP to complete provider registration"
      />
      <AuthLayout>
        <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
          <div className="mb-8 text-center">
            <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
              {t('verification.title')}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('verification.subtitle')}{' '}
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {registrationData.identifier}
              </span>
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <label className="block mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('verification.enterCode')}
              </label>
              <OtpInput
                value={otp}
                onChange={handleOtpChange}
                onComplete={handleOtpComplete}
                error={!!errors.otp}
                disabled={isLoading}
                autoFocus
              />
              {errors.otp && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.otp.message}
                </p>
              )}
            </div>



            {verifyOtpMutation.error && (
              <ErrorDisplay
                error={verifyOtpMutation.error}
                variant="banner"
                size="sm"
              />
            )}

            <Button
              type="submit"
              className="w-full"
              size="sm"
              disabled={isLoading || otp.length !== 6}
            >
              {isLoading ? t('verification.verifying') || 'Verifying...' : t('verification.verifyButton')}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {canResend ? (
                <button
                  onClick={handleResendOtp}
                  disabled={isLoading}
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400 font-medium"
                >
                  {t('verification.resendCode')}
                </button>
              ) : (
                <>
                  {t('verification.codeExpires')}{' '}
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {formatTime(timeLeft)}
                  </span>
                </>
              )}
            </p>
          </div>
        </div>
      </AuthLayout>
    </>
  );
}
