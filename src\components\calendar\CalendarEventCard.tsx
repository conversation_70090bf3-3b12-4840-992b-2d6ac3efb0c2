import React from 'react';
import { Appointment } from '../../types';
import { formatLocalTime } from '../../utils/timezone';

interface CalendarEventCardProps {
  appointment: Appointment;
  isCompact?: boolean;
  onClick?: () => void;
}

const CalendarEventCard: React.FC<CalendarEventCardProps> = ({
  appointment,
  isCompact = false
}) => {
  if (isCompact) {
    return (
      <div className="relative h-full w-full">
        <div className="h-full flex flex-col items-center justify-center p-1">
          <div className="w-3 h-3 rounded-full bg-current opacity-90 mb-1"></div>
          <div className="text-xs text-white/90 font-medium text-center leading-none">
            {formatLocalTime(appointment.expectedAppointmentStartTime, {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-full w-full">
      <div className="h-full p-2 flex flex-col justify-center">
        <div className="flex items-center justify-between min-h-0 gap-1">
          <div className="flex-1 min-w-0">
            <div className="text-xs font-medium truncate text-white leading-tight">
              {appointment.service?.title || 'Service'}
            </div>
          </div>
          <div className="flex-shrink-0">
            <div className="text-xs text-white/90 font-medium">
              {formatLocalTime(appointment.expectedAppointmentStartTime, {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              })}
            </div>
          </div>
        </div>

        {/* Customer name on second line if there's space */}
        <div className="mt-0.5">
          <div className="text-xs text-white/80 truncate leading-tight">
            {appointment.customer?.firstName} {appointment.customer?.lastName}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarEventCard;
