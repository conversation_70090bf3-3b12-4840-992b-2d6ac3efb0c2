import React from 'react';
import { Control, Controller, useFieldArray, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import Button from '../ui/button/Button';
import { useManagementTranslation } from '../../hooks/useTranslation';
import { useRTL } from '../../context/LanguageContext';

// Icons
const PlusIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const TrashBinIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

const TimeIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

export interface OpeningHour {
  dayOfWeek: string;
  isActive: boolean;
  hours: Array<{
    timeFrom: string;
    timeTo: string;
  }>;
}

interface OpeningHoursFormProps {
  control: Control<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  fieldName?: string;
  disabled?: boolean;
}

export default function OpeningHoursForm({ 
  control, 
  watch, 
  setValue, 
  fieldName = 'openingHours',
  disabled = false 
}: OpeningHoursFormProps) {
  const { t: ft, currentLanguage } = useManagementTranslation();
  const { isRTL, direction } = useRTL();

  // Fixed days of the week in order
  const DAYS_OF_WEEK = [
    { value: 'Monday', key: 'monday' },
    { value: 'Tuesday', key: 'tuesday' },
    { value: 'Wednesday', key: 'wednesday' },
    { value: 'Thursday', key: 'thursday' },
    { value: 'Friday', key: 'friday' },
    { value: 'Saturday', key: 'saturday' },
    { value: 'Sunday', key: 'sunday' }
  ];

  // Custom translations for opening hours
  const openingHoursTranslations = {
    ar: {
      openingHours: "ساعات العمل",
      standardHours: "ساعات عمل قياسية",
      clearAll: "مسح الكل",
      monday: "الاثنين",
      tuesday: "الثلاثاء", 
      wednesday: "الأربعاء",
      thursday: "الخميس",
      friday: "الجمعة",
      saturday: "السبت",
      sunday: "الأحد",
      addTimeSlot: "إضافة فترة زمنية",
      from: "من",
      to: "إلى",
      remove: "إزالة",
      closed: "مغلق",
      noTimeSlots: "لا توجد فترات زمنية. انقر على \"إضافة فترة زمنية\" للبدء."
    },
    en: {
      openingHours: "Opening Hours",
      standardHours: "Standard Hours",
      clearAll: "Clear All",
      monday: "Monday",
      tuesday: "Tuesday",
      wednesday: "Wednesday", 
      thursday: "Thursday",
      friday: "Friday",
      saturday: "Saturday",
      sunday: "Sunday",
      addTimeSlot: "Add Time Slot",
      from: "From",
      to: "To",
      remove: "Remove",
      closed: "Closed",
      noTimeSlots: "No time slots. Click \"Add Time Slot\" to start."
    },
    fr: {
      openingHours: "Heures d'ouverture",
      standardHours: "Heures standard",
      clearAll: "Tout effacer",
      monday: "Lundi",
      tuesday: "Mardi",
      wednesday: "Mercredi",
      thursday: "Jeudi", 
      friday: "Vendredi",
      saturday: "Samedi",
      sunday: "Dimanche",
      addTimeSlot: "Ajouter un créneau",
      from: "De",
      to: "À",
      remove: "Supprimer",
      closed: "Fermé",
      noTimeSlots: "Aucun créneau horaire. Cliquez sur \"Ajouter un créneau\" pour commencer."
    }
  };

  const currentLang = currentLanguage as keyof typeof openingHoursTranslations;
  const ot = (key: keyof typeof openingHoursTranslations.ar) => {
    return openingHoursTranslations[currentLang]?.[key] || openingHoursTranslations.en[key] || key;
  };

  // Initialize opening hours with all 7 days
  const initializeOpeningHours = () => {
    const currentHours = watch(fieldName) || [];
    const initializedHours = DAYS_OF_WEEK.map(day => {
      const existingDay = currentHours.find((h: OpeningHour) => h.dayOfWeek === day.value);
      return existingDay || {
        dayOfWeek: day.value,
        isActive: false,
        hours: []
      };
    });
    setValue(fieldName, initializedHours);
  };

  // Initialize on component mount if not already initialized
  React.useEffect(() => {
    const currentHours = watch(fieldName) || [];
    if (currentHours.length !== 7) {
      initializeOpeningHours();
    }
  }, []);

  const setupStandardHours = () => {
    const standardHours = DAYS_OF_WEEK.map(day => ({
      dayOfWeek: day.value,
      isActive: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].includes(day.value),
      hours: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].includes(day.value) 
        ? [{ timeFrom: '09:00', timeTo: '17:00' }] 
        : []
    }));
    setValue(fieldName, standardHours);
  };

  const clearAllHours = () => {
    const clearedHours = DAYS_OF_WEEK.map(day => ({
      dayOfWeek: day.value,
      isActive: false,
      hours: []
    }));
    setValue(fieldName, clearedHours);
  };

  const toggleDay = (dayIndex: number) => {
    const currentDay = watch(`${fieldName}.${dayIndex}`);
    const newIsActive = !currentDay.isActive;
    setValue(`${fieldName}.${dayIndex}.isActive`, newIsActive);
    
    // If activating the day and no hours exist, add a default time slot
    if (newIsActive && (!currentDay.hours || currentDay.hours.length === 0)) {
      setValue(`${fieldName}.${dayIndex}.hours`, [{ timeFrom: '09:00', timeTo: '17:00' }]);
    }
    // If deactivating the day, clear all hours
    if (!newIsActive) {
      setValue(`${fieldName}.${dayIndex}.hours`, []);
    }
  };

  const addTimeSlot = (dayIndex: number) => {
    const currentHours = watch(`${fieldName}.${dayIndex}.hours`) || [];
    setValue(`${fieldName}.${dayIndex}.hours`, [
      ...currentHours,
      { timeFrom: '09:00', timeTo: '17:00' }
    ]);
  };

  const removeTimeSlot = (dayIndex: number, hourIndex: number) => {
    const currentHours = watch(`${fieldName}.${dayIndex}.hours`) || [];
    const newHours = currentHours.filter((_: any, index: number) => index !== hourIndex);
    setValue(`${fieldName}.${dayIndex}.hours`, newHours);
    
    // If no hours left, deactivate the day
    if (newHours.length === 0) {
      setValue(`${fieldName}.${dayIndex}.isActive`, false);
    }
  };

  const openingHours = watch(fieldName) || [];

  return (
    <div className="space-y-4" dir={direction}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <TimeIcon className="w-5 h-5 mr-2" />
          {ot('openingHours')}
        </h3>
        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={setupStandardHours}
            disabled={disabled}
          >
            {ot('standardHours')}
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={clearAllHours}
            disabled={disabled}
            className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          >
            {ot('clearAll')}
          </Button>
        </div>
      </div>

      {/* Days */}
      <div className="space-y-3">
        {DAYS_OF_WEEK.map((day, dayIndex) => {
          const dayData = openingHours[dayIndex] || { dayOfWeek: day.value, isActive: false, hours: [] };
          const isActive = dayData.isActive;
          
          return (
            <div 
              key={day.value} 
              className={`border rounded-lg p-4 transition-all ${
                isActive 
                  ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' 
                  : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
              }`}
            >
              {/* Day Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <Controller
                    name={`${fieldName}.${dayIndex}.isActive`}
                    control={control}
                    render={({ field }) => (
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={field.value || false}
                          onChange={() => toggleDay(dayIndex)}
                          disabled={disabled}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className={`ml-2 text-sm font-medium ${
                          isActive 
                            ? 'text-blue-900 dark:text-blue-100' 
                            : 'text-gray-600 dark:text-gray-400'
                        }`}>
                          {ot(day.key as keyof typeof openingHoursTranslations.ar)}
                        </span>
                      </label>
                    )}
                  />
                </div>
                
                {!isActive && (
                  <span className="text-sm text-gray-500 dark:text-gray-400 italic">
                    {ot('closed')}
                  </span>
                )}
                
                {isActive && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addTimeSlot(dayIndex)}
                    disabled={disabled}
                    startIcon={<PlusIcon className="w-4 h-4" />}
                  >
                    {ot('addTimeSlot')}
                  </Button>
                )}
              </div>

              {/* Time Slots */}
              {isActive && (
                <div className="space-y-2 ml-6">
                  {(dayData.hours || []).map((_, hourIndex) => (
                    <div key={hourIndex} className="flex items-center space-x-3">
                      <div className="flex-1">
                        <Controller
                          name={`${fieldName}.${dayIndex}.hours.${hourIndex}.timeFrom`}
                          control={control}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="time"
                              disabled={disabled}
                              className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-800 shadow-sm focus:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500/20 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
                            />
                          )}
                        />
                      </div>
                      
                      <span className="text-gray-500 dark:text-gray-400 text-sm">
                        {ot('to')}
                      </span>
                      
                      <div className="flex-1">
                        <Controller
                          name={`${fieldName}.${dayIndex}.hours.${hourIndex}.timeTo`}
                          control={control}
                          render={({ field }) => (
                            <input
                              {...field}
                              type="time"
                              disabled={disabled}
                              className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-800 shadow-sm focus:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500/20 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
                            />
                          )}
                        />
                      </div>
                      
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTimeSlot(dayIndex, hourIndex)}
                        disabled={disabled}
                        className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                      >
                        <TrashBinIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  
                  {(!dayData.hours || dayData.hours.length === 0) && (
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-2 italic">
                      {ot('noTimeSlots')}
                    </p>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
