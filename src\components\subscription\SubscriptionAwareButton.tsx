import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { useAuth } from '../../context/AuthContext';
import { CheckoutModal } from './';
import { useModal } from '../../hooks/useModal';
import { useManagementTranslation } from '../../hooks/useTranslation';
import toast from 'react-hot-toast';

interface SubscriptionAwareButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  requiresCredits?: number;
  requiresActiveSubscription?: boolean;
  requiresQueueAvailability?: boolean;
  upgradeMessage?: string;
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
  className?: string;
  disabled?: boolean;
  showIcon?: boolean;
}

export default function SubscriptionAwareButton({
  children,
  onClick,
  requiresCredits = 0,
  requiresActiveSubscription = false,
  requiresQueueAvailability = false,
  upgradeMessage = 'Upgrade your plan to access this feature',
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  showIcon = true,
}: SubscriptionAwareButtonProps) {
  const { 
    subscription, 
    subscriptionLoading, 
    hasActiveSubscription, 
    getCurrentCredits, 
    getCurrentQueueLimit,
    isAtQueueLimit 
  } = useAuth();
  
  const { isOpen: isCheckoutOpen, openModal: openCheckout, closeModal: closeCheckout } = useModal();

  const checkSubscriptionRequirements = (): { canProceed: boolean; reason?: string } => {
    // Check if subscription data is loading
    if (subscriptionLoading) {
      return { canProceed: false, reason: 'Loading subscription data...' };
    }

    // Check if active subscription is required
    if (requiresActiveSubscription && !hasActiveSubscription()) {
      return { 
        canProceed: false, 
        reason: 'Active subscription required. Please upgrade your plan.' 
      };
    }

    // Check credit requirements
    if (requiresCredits > 0) {
      const currentCredits = getCurrentCredits();
      if (currentCredits < requiresCredits) {
        return { 
          canProceed: false, 
          reason: `Insufficient credits. You need ${requiresCredits} credits but only have ${currentCredits}.` 
        };
      }
    }

    // Check queue availability
    if (requiresQueueAvailability && isAtQueueLimit()) {
      const currentLimit = getCurrentQueueLimit();
      return { 
        canProceed: false, 
        reason: `Queue limit reached. You can create up to ${currentLimit} queue(s). Please upgrade to create more.` 
      };
    }

    return { canProceed: true };
  };

  const handleClick = () => {
    const { canProceed, reason } = checkSubscriptionRequirements();
    
    if (!canProceed) {
      toast.error(reason || upgradeMessage);
      openCheckout();
      return;
    }

    // All requirements met, proceed with original action
    if (onClick) {
      onClick();
    }
  };

  const isButtonDisabled = () => {
    if (disabled) return true;
    if (subscriptionLoading) return true;
    
    const { canProceed } = checkSubscriptionRequirements();
    return false; // Don't disable, show upgrade modal instead
  };

  const getButtonText = () => {
    if (subscriptionLoading) return 'Loading...';
    
    const { canProceed, reason } = checkSubscriptionRequirements();
    if (!canProceed && showIcon) {
      return (
        <span className="flex items-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>{children}</span>
        </span>
      );
    }
    
    return children;
  };

  return (
    <>
      <Button
        onClick={handleClick}
        variant={variant}
        size={size}
        disabled={isButtonDisabled()}
        className={className}
      >
        {getButtonText()}
      </Button>

      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={closeCheckout}
        title="subscription.upgradeRequired"
        showPlanComparison={true}
      />
    </>
  );
}

// Specialized components for common use cases
interface CreateQueueButtonProps {
  onCreateQueue: () => void;
  className?: string;
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
}

export function CreateQueueButton({
  onCreateQueue,
  className = '',
  variant = 'primary',
  size = 'md'
}: CreateQueueButtonProps) {
  const { t } = useManagementTranslation();

  return (
    <SubscriptionAwareButton
      onClick={onCreateQueue}
      requiresQueueAvailability={true}
      // requiresActiveSubscription={true}
      upgradeMessage={t('queues.form.upgradeToCreateMore')}
      variant={variant}
      size={size}
      className={className}
    >
      {t('queues.createQueue')}
    </SubscriptionAwareButton>
  );
}

interface CreateServiceButtonProps {
  onCreateService: () => void;
  className?: string;
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
}

export function CreateServiceButton({
  onCreateService,
  className = '',
  variant = 'primary',
  size = 'md'
}: CreateServiceButtonProps) {
  const { t } = useManagementTranslation();

  return (
    <SubscriptionAwareButton
      onClick={onCreateService}
      // requiresCredits={1}
      upgradeMessage={t('services.form.upgradeToCreateMore', 'Upgrade your plan to create more services')}
      variant={variant}
      size={size}
      className={className}
    >
      {t('services.createService', 'Create Service')}
    </SubscriptionAwareButton>
  );
}

interface BookAppointmentButtonProps {
  onBookAppointment: () => void;
  className?: string;
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
}

export function BookAppointmentButton({ 
  onBookAppointment, 
  className = '', 
  variant = 'primary',
  size = 'md' 
}: BookAppointmentButtonProps) {
  return (
    <SubscriptionAwareButton
      onClick={onBookAppointment}
      requiresCredits={1}
      upgradeMessage="Upgrade your plan to book more appointments"
      variant={variant}
      size={size}
      className={className}
    >
      Book Appointment
    </SubscriptionAwareButton>
  );
}

interface PremiumFeatureButtonProps {
  onAccessFeature: () => void;
  featureName: string;
  className?: string;
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
}

export function PremiumFeatureButton({ 
  onAccessFeature, 
  featureName,
  className = '', 
  variant = 'primary',
  size = 'md' 
}: PremiumFeatureButtonProps) {
  return (
    <SubscriptionAwareButton
      onClick={onAccessFeature}
      requiresActiveSubscription={true}
      upgradeMessage={`Upgrade to access ${featureName}`}
      variant={variant}
      size={size}
      className={className}
    >
      Access {featureName}
    </SubscriptionAwareButton>
  );
}
