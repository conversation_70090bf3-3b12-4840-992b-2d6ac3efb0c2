import React, { useState } from 'react';
import Button from "../../../components/ui/button/Button";
import { Modal } from "../../../components/ui/modal";
import { useModal } from "../../../hooks/useModal";
import { ErrorDisplay } from "../../../components/error";
import { useConfirmation } from "../../../hooks/useConfirmation";
import ConfirmationDialog from "../../../components/ui/confirmation/ConfirmationDialog";
import { useServices, useDeleteService } from "../../../hooks/useServices";
import ServiceForm from "../../../components/services/ServiceForm";
import ServiceCard from "../../../components/services/ServiceCard";
import ServiceCategoryManager from "../../../components/services/ServiceCategoryManager";
import {
  CreateServiceButton,
  UsageWarningBanner
} from "../../../components/subscription";
import { Service, ServiceFilters } from "../../../types";
import { useManagementTranslation, useCommonTranslation } from "../../../hooks/useTranslation";
import { useRTL } from "../../../context/LanguageContext";

// Separate component for service results to prevent full page re-render
interface ServiceResultsProps {
  filters: ServiceFilters;
  onEditService: (service: Service) => void;
  onDeleteService: (id: number) => void;
  onCreateService: () => void;
  deleteServiceMutation: any;
}

const ServiceResults: React.FC<ServiceResultsProps> = React.memo(({
  filters,
  onEditService,
  onDeleteService,
  onCreateService,
  deleteServiceMutation
}) => {
  const { data: services, isLoading, error } = useServices(filters);
  const { t } = useManagementTranslation();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600"></div>
        <p className="ml-3 text-gray-600 dark:text-gray-400">Loading services...</p>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay
        error={error}
        title="Failed to load services"
        variant="card"
        showRetry
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (!services || services.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('services.noServicesFound', 'No services found')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {t('services.getStarted', 'Get started by creating your first service.')}
        </p>
        <CreateServiceButton onCreateService={onCreateService} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('services.yourServices', 'Your Services')} ({services.length})
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('services.manageOfferings', 'Manage your service offerings and pricing')}
          </p>
        </div>
        <CreateServiceButton onCreateService={onCreateService} />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service) => (
          <ServiceCard
            key={service.id}
            service={service}
            onEdit={() => onEditService(service)}
            onDelete={() => onDeleteService(service.id)}
            isDeleting={deleteServiceMutation.isPending}
          />
        ))}
      </div>
    </div>
  );
});

ServiceResults.displayName = 'ServiceResults';

export default function ServicesTab() {
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { isRTL, direction } = useRTL();
  const [filters, setFilters] = useState<ServiceFilters>({});
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [modalType, setModalType] = useState<'service' | 'category' | null>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const confirmation = useConfirmation();

  const deleteServiceMutation = useDeleteService();

  const handleCreateService = () => {
    setEditingService(null);
    setModalType('service');
    openModal();
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setModalType('service');
    openModal();
  };

  const handleDeleteService = async (id: number) => {
    const confirmed = await confirmation.confirm({
      title: t('services.deleteConfirmTitle', 'Delete Service'),
      message: t('services.deleteConfirmMessage', 'Are you sure you want to delete this service? This action cannot be undone and will remove all associated data.'),
      confirmText: tCommon('actions.delete'),
      cancelText: tCommon('actions.cancel'),
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteServiceMutation.mutateAsync(id);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleServiceFormSuccess = () => {
    closeModal();
    setEditingService(null);
  };



  return (
    <div className="space-y-6" dir={direction}>
      {/* Usage Warning Banner */}
      {/* <UsageWarningBanner /> */}

      {/* Category Management */}
      <div className={`flex items-center  justify-between`}>
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('services.title', 'Services Management')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('services.subtitle', 'Create and manage your service offerings')}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            setModalType('category');
            openModal();
          }}
          className="text-brand-600 border-brand-300 hover:bg-brand-50 dark:text-brand-400 dark:border-brand-700 dark:hover:bg-brand-900/20"
        >
          {t('services.manageCategories', 'Manage Categories')}
        </Button>
      </div>

      {/* Services List */}
      <ServiceResults
        filters={filters}
        onEditService={handleEditService}
        onDeleteService={handleDeleteService}
        onCreateService={handleCreateService}
        deleteServiceMutation={deleteServiceMutation}
      />

      {/* Service Modal */}
      <Modal
        isOpen={isOpen && modalType === 'service'}
        onClose={closeModal}
        className="max-w-2xl"
        showCloseButton={true}
      >
        <ServiceForm
          service={editingService}
          onSuccess={handleServiceFormSuccess}
          onClose={closeModal}
        />
      </Modal>

      {/* Category Manager Modal */}
      <Modal
        isOpen={isOpen && modalType === 'category'}
        onClose={closeModal}
        className="max-w-4xl"
        showCloseButton={true}
      >
        <ServiceCategoryManager
          onClose={closeModal}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
