import { SidebarProvider, useSidebar } from "../context/SidebarContext";
import { Outlet } from "react-router";
import AppHeader from "./AppHeader";
import Backdrop from "./Backdrop";
import AppSidebar from "./AppSidebar";
import RealTimeProvider from "../components/providers/RealTimeProvider";
import { ProfileCompletionManager } from "../components/profile-completion";
import { useLanguage } from "../context/LanguageContext";
import { useAutoLanguageInvalidation } from "../hooks/useLanguageQueryInvalidation";

const LayoutContent: React.FC = () => {
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();
  const { isRTL } = useLanguage();

  // Enable automatic query invalidation when language changes
  // This ensures all data is refetched with the new language
  useAutoLanguageInvalidation({
    invalidateAll: true, // Invalidate all queries when language changes
    refetchActive: false, // Don't force immediate refetch, let components handle it naturally
  });

  return (
    <div className="min-h-screen xl:flex">
      <div>
        <AppSidebar />
        <Backdrop />
      </div>
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${
          isExpanded || isHovered
            ? isRTL ? "lg:mr-[290px]" : "lg:ml-[290px]"
            : isRTL ? "lg:mr-[90px]" : "lg:ml-[90px]"
        } ${isMobileOpen ? (isRTL ? "mr-0" : "ml-0") : ""}`}
      >
        <AppHeader />
        <div className="p-4 mx-auto max-w-(--breakpoint-2xl) md:p-6">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

const AppLayout: React.FC = () => {
  return (
    <SidebarProvider>
      <RealTimeProvider>
        <ProfileCompletionManager resetOnMount={false}>
          <LayoutContent />
        </ProfileCompletionManager>
      </RealTimeProvider>
    </SidebarProvider>
  );
};

export default AppLayout;
