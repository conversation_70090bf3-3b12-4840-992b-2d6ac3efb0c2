import React from 'react';
import { SubscriptionPlan } from '../../types';
import Button from '../ui/button/Button';
import { useManagementTranslation, useTranslation } from '../../hooks/useTranslation';

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isCurrentPlan?: boolean;
  isRecommended?: boolean;
  isLoading?: boolean;
  onSubscribe?: (planId: string) => void;
  onUpgrade?: (planId: string) => void;
  className?: string;
  type?: 'vertical' | 'horizontal';
}

export default function SubscriptionPlanCard({
  plan,
  isCurrentPlan = false,
  isRecommended = false,
  isLoading = false,
  onSubscribe,
  onUpgrade,
  className = '',
  type = 'vertical',
}: SubscriptionPlanCardProps) {
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tDashboard } = useTranslation('dashboard');

  // Function to translate plan names
  const translatePlanName = (planName: string): string => {
    const normalizedName = planName.toLowerCase();
    switch (normalizedName) {
      case 'free':
        return tDashboard('subscription.free');
      case 'starter':
        return tDashboard('subscription.starter');
      case 'business':
        return tDashboard('subscription.business');
      case 'enterprise':
        return tDashboard('subscription.enterprise');
      default:
        return planName; // Return original name if no translation found
    }
  };

  // Custom translations for plan card buttons
  const planCardTranslations = {
    ar: {
      currentPlan: "الخطة الحالية",
      purchaseCredits: "شراء رصيد",
      getStarted: "ابدأ الآن",
      subscribe: "اشترك"
    },
    en: {
      currentPlan: "Current Plan",
      purchaseCredits: "Purchase Credits",
      getStarted: "Get Started",
      subscribe: "Subscribe"
    },
    fr: {
      currentPlan: "Plan actuel",
      purchaseCredits: "Acheter des crédits",
      getStarted: "Commencer",
      subscribe: "S'abonner"
    }
  };

  const currentLang = currentLanguage as keyof typeof planCardTranslations;
  const pct = (key: keyof typeof planCardTranslations.ar) =>
    planCardTranslations[currentLang]?.[key] || planCardTranslations.en[key] || key;

  // Plan content translation mapping
  const translatePlanContent = (text: string): string => {
    // Handle dynamic credit amounts in descriptions
    const creditAmountMatch = text.match(/One-time purchase of (\d+) credits for your account/);
    if (creditAmountMatch) {
      const amount = creditAmountMatch[1];
      if (currentLang === 'ar') {
        return `شراء لمرة واحدة من ${amount} رصيد لحسابك`;
      } else if (currentLang === 'fr') {
        return `Achat unique de ${amount} crédits pour votre compte`;
      }
      return text; // Return original for English
    }

    const contentTranslations: Record<string, Record<string, string>> = {
      ar: {
        'Our most popular plan': 'خطتنا الأكثر شعبية',
        'All you need to get started': 'كل ما تحتاجه للبدء',
        'Free plan for everyone': 'خطة مجانية للجميع',
        'Priority customer support': 'دعم العملاء المتميز',
        'Basic support': 'الدعم الأساسي',
        'No expiration date': 'لا يوجد تاريخ انتهاء',
        'Queues 10': 'الطوابير 10',
        'Queues 3': 'الطوابير 3',
        'Queue 1': 'طابور 1',
        'One-time Purchase': 'شراء لمرة واحدة',
        'Monthly Subscription': 'اشتراك شهري'
      },
      en: {
        'Our most popular plan': 'Our most popular plan',
        'All you need to get started': 'All you need to get started',
        'Free plan for everyone': 'Free plan for everyone',
        'Priority customer support': 'Priority customer support',
        'Basic support': 'Basic support',
        'No expiration date': 'No expiration date',
        'Queues 10': 'Queues 10',
        'Queues 3': 'Queues 3',
        'Queue 1': 'Queue 1',
        'One-time Purchase': 'One-time Purchase',
        'Monthly Subscription': 'Monthly Subscription'
      },
      fr: {
        'Our most popular plan': 'Notre plan le plus populaire',
        'All you need to get started': 'Tout ce dont vous avez besoin pour commencer',
        'Free plan for everyone': 'Plan gratuit pour tous',
        'Priority customer support': 'Support client prioritaire',
        'Basic support': 'Support de base',
        'No expiration date': 'Aucune date d\'expiration',
        'Queues 10': 'Files d\'attente 10',
        'Queues 3': 'Files d\'attente 3',
        'Queue 1': 'File d\'attente 1',
        'One-time Purchase': 'Achat unique',
        'Monthly Subscription': 'Abonnement mensuel'
      }
    };

    return contentTranslations[currentLang]?.[text] || contentTranslations['en']?.[text] || text;
  };

  // Additional badge translations
  const getBadgeText = (type: 'current' | 'recommended' | 'mostPopular') => {
    const badgeTranslations = {
      ar: {
        current: '✓ الحالية',
        recommended: 'موصى بها',
        mostPopular: 'الأكثر شعبية'
      },
      en: {
        current: '✓ Current',
        recommended: 'Recommended',
        mostPopular: 'Most Popular'
      },
      fr: {
        current: '✓ Actuel',
        recommended: 'Recommandé',
        mostPopular: 'Le plus populaire'
      }
    };
    return badgeTranslations[currentLang]?.[type] || badgeTranslations.en[type];
  };
  
  const handleActionClick = () => {
    if (isCurrentPlan) return;
    
    if (onUpgrade && !plan.isOneTime) {
      onUpgrade(plan.id);
    } else if (onSubscribe) {
      onSubscribe(plan.id);
    }
  };

  const getActionButtonText = () => {
    if (isCurrentPlan) return pct('currentPlan');
    if (plan.isOneTime) return pct('purchaseCredits');
    if (plan.id === 'free') return pct('getStarted');
    return pct('subscribe');
  };

  const getActionButtonVariant = () => {
    if (isCurrentPlan) return 'outline';
    return 'primary';
  };

  const getPlanTypeIcon = () => {
    if (plan.isOneTime) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      );
    }
    return (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  };

  const getCreditsDisplay = () => {
    if (plan.effect.kind === 'credits') {
      return pct('purchaseCredits').replace('Purchase ', '').replace('Acheter des ', '').replace('شراء ', '') + ` ${plan.effect.amount}`;
    }
    return `${tDashboard('subscription.creditsPerMonth')} ${plan.effect.amount}`;
  };

  const getQueuesDisplay = () => {
    if (plan.effect.queues === null) return null;
    const queueText = plan.effect.queues === 1
      ? (currentLang === 'ar' ? 'طابور' : currentLang === 'fr' ? 'File d\'attente' : 'Queue')
      : (currentLang === 'ar' ? 'الطوابير' : currentLang === 'fr' ? 'Files d\'attente' : 'Queues');
    return `${queueText} ${plan.effect.queues}`;
  };

  // Horizontal layout for checkout modal
  if (type === 'horizontal') {
    return (
      <div
        className={`
          relative bg-white dark:bg-gray-800 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg
          ${isCurrentPlan
            ? 'border-brand-500 bg-brand-50/50 dark:bg-brand-900/10'
            : isRecommended
              ? 'border-brand-300 dark:border-brand-600'
              : 'border-gray-200 dark:border-gray-700 hover:border-brand-200 dark:hover:border-brand-700'
          }
          ${className}
        `}
      >
        {/* Current Plan Badge */}
        {isCurrentPlan && (
          <div className="absolute -top-2 -right-2 z-10">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
              {getBadgeText('current')}
            </span>
          </div>
        )}

        <div className="p-4">
          {/* Header Row */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  {translatePlanName(plan.name)}
                </h3>
                {isRecommended && !isCurrentPlan && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-brand-500 text-white">
                    {getBadgeText('recommended')}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {getCreditsDisplay()}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {plan.price}
              </div>
              {plan.isSubscription && plan.id !== 'free' && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {currentLang === 'ar' ? '/شهر' : currentLang === 'fr' ? '/mois' : '/month'}
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <p className={`text-sm text-gray-600 dark:text-gray-400 mb-3 ${currentLang === 'ar' ? 'text-right' : 'text-left'}`}>
            {translatePlanContent(plan.description)}
          </p>

          {/* Action Button */}
          <Button
            onClick={handleActionClick}
            variant={getActionButtonVariant()}
            size="sm"
            disabled={isCurrentPlan || isLoading}
            className="w-full"
          >
            {isLoading ? 'Processing...' : getActionButtonText()}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`
        relative bg-white dark:bg-gray-800 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg
        ${isCurrentPlan 
          ? 'border-brand-500 bg-brand-50/50 dark:bg-brand-900/10' 
          : isRecommended 
            ? 'border-brand-300 dark:border-brand-600' 
            : 'border-gray-200 dark:border-gray-700 hover:border-brand-200 dark:hover:border-brand-700'
        }
        ${className}
      `}
    >
      {/* Recommended Badge */}
      {isRecommended && !isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-brand-500 text-white">
            {getBadgeText('mostPopular')}
          </span>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
            Current Plan
          </span>
        </div>
      )}

      <div className="p-6">
        {/* Plan Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-3">
            <div className={`p-2 rounded-lg ${isCurrentPlan ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'}`}>
              {getPlanTypeIcon()}
            </div>
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {translatePlanName(plan.name)}
          </h3>
          
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {translatePlanContent(plan.description)}
          </p>

          {/* Price */}
          <div className="mb-4">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">
              {plan.price}
            </span>
            {plan.isSubscription && plan.id !== 'free' && (
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                {currentLang === 'ar' ? '/شهر' : currentLang === 'fr' ? '/mois' : '/month'}
              </span>
            )}
          </div>

          {/* Credits and Queues Info */}
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
            <div className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span>{getCreditsDisplay()}</span>
            </div>
            
            {getQueuesDisplay() && (
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span>{getQueuesDisplay()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Features List */}
        <div className="mb-6">
          <ul className="space-y-3">
            {plan.features.map((feature, index) => (
              <li key={index} className={`flex items-start ${currentLang === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 mt-0.5 ${currentLang === 'ar' ? 'ml-3' : 'mr-3'}`}>
                  <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span className={`text-sm text-gray-600 dark:text-gray-400 ${currentLang === 'ar' ? 'text-right' : 'text-left'}`}>
                  {translatePlanContent(feature)}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Action Button */}
        <Button
          onClick={handleActionClick}
          variant={getActionButtonVariant()}
          size="md"
          disabled={isCurrentPlan || isLoading}
          className="w-full"
        >
          {isLoading ? 'Processing...' : getActionButtonText()}
        </Button>

        {/* Plan Type Indicator */}
        <div className="mt-4 text-center">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            plan.isOneTime 
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
              : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
          }`}>
            {plan.isOneTime ? 'One-time Purchase' : 'Monthly Subscription'}
          </span>
        </div>
      </div>
    </div>
  );
}
