import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { ErrorDisplay } from '../error';
import { useAuth } from '../../context/AuthContext';
import {
  useProviderProfile,
  useUpdateProviderProfile,
  useUploadLogo,
  useDeleteLogo
} from '../../hooks/useProvider';
import { useProviderCategories } from '../../hooks/useAuthMutations';
import { ProviderProfileUpdateRequest } from '../../types';
import TwoStepCategorySelector from '../form/TwoStepCategorySelector';
import FileUpload from '../ui/FileUpload';
import { useTranslation } from '../../hooks/useTranslation';

// Validation schema - will be created inside component to access translations
const createProfileSchema = (t: any) => z.object({
  title: z.string().min(2, t('forms.validation.businessNameMin')),
  phone: z.string().min(10, t('forms.validation.phoneMin')),
  presentation: z.string().optional(),
  providerCategoryId: z.number().min(1, t('forms.validation.categoryRequired')),
});

type ProfileFormData = z.infer<ReturnType<typeof createProfileSchema>>;

interface BusinessInfoSetupModalProps {
  onComplete: () => void;
  onCancel: () => void;
}

export default function BusinessInfoSetupModal({ onComplete, onCancel }: BusinessInfoSetupModalProps) {
  const { user, provider } = useAuth();
  const { t } = useTranslation('forms');
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoUploadProgress, setLogoUploadProgress] = useState(0);

  const { data: profileData, isLoading: profileLoading } = useProviderProfile();
  const { data: categories } = useProviderCategories();
  const updateProfileMutation = useUpdateProviderProfile();
  const uploadLogoMutation = useUploadLogo();
  const deleteLogoMutation = useDeleteLogo();

  // Create schema with translations
  const profileSchema = createProfileSchema(t);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      title: '',
      phone: '',
      presentation: '',
      providerCategoryId: 0,
    },
  });

  // Load existing data
  useEffect(() => {
    if (profileData) {
      reset({
        title: profileData.title || '',
        phone: profileData.phone || '',
        presentation: profileData.presentation || '',
        providerCategoryId: profileData.providerCategoryId || 0,
      });
    }
  }, [profileData, reset]);

  const isLoading = profileLoading || isSubmitting || updateProfileMutation.isPending || uploadLogoMutation.isPending;

  const onSubmit = async (data: ProfileFormData) => {
    try {
      // Update profile
      await updateProfileMutation.mutateAsync(data);
      
      // Upload logo if provided
      if (logoFile) {
        await uploadLogoMutation.mutateAsync(logoFile);
      }
      
      onComplete();
    } catch (error) {
      // Error handled by mutations
    }
  };

  const handleLogoSelect = (file: File) => {
    setLogoFile(file);
  };

  const handleLogoUpload = async () => {
    if (logoFile) {
      try {
        await uploadLogoMutation.mutateAsync({
          file: logoFile,
          onProgress: setLogoUploadProgress,
        });
        setLogoFile(null);
        setLogoUploadProgress(0);
      } catch (error) {
        setLogoUploadProgress(0);
        // Error handled by mutation
      }
    }
  };

  const handleLogoRemove = async () => {
    try {
      await deleteLogoMutation.mutateAsync();
      setLogoFile(null);
    } catch (error) {
      // Error handled by mutation
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Business Logo Section */}
      <FileUpload
        label={t('labels.businessLogo')}
        description={t('placeholders.uploadBusinessLogo')}
        currentImageUrl={profileData?.logo}
        onFileSelect={handleLogoSelect}
        onUpload={handleLogoUpload}
        onRemove={handleLogoRemove}
        isUploading={uploadLogoMutation.isPending}
        uploadProgress={logoUploadProgress}
        disabled={isLoading}
        showUploadButton={!!logoFile}
        showRemoveButton={!!profileData?.logo || !!logoFile}
      />

      {/* Business Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label>
            {t('labels.businessName')}<span className="text-red-500">*</span>
          </Label>
          <Input
            {...register("title")}
            placeholder={t('placeholders.enterBusinessName')}
            disabled={isLoading}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.title.message}
            </p>
          )}
        </div>

        <div>
          <Label>
            {t('labels.phone')}<span className="text-red-500">*</span>
          </Label>
          <Input
            {...register("phone")}
            type="tel"
            placeholder={t('placeholders.enterPhoneNumber')}
            disabled={isLoading}
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.phone.message}
            </p>
          )}
        </div>
      </div>

      {/* Business Category */}
      <div>
        <TwoStepCategorySelector
          categories={categories || []}
          value={watch("providerCategoryId") || undefined}
          onChange={(categoryId) => {
            setValue("providerCategoryId", categoryId || 0);
          }}
          required={true}
        />
        {errors.providerCategoryId && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.providerCategoryId.message}
          </p>
        )}
      </div>

      {/* Business Description */}
      <div>
        <Label>{t('labels.businessDescription')}</Label>
        <textarea
          {...register("presentation")}
          rows={4}
          placeholder={t('placeholders.tellCustomersAboutBusiness')}
          disabled={isLoading}
          className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
        />
      </div>

      {/* Error Display */}
      {updateProfileMutation.error && (
        <ErrorDisplay
          error={updateProfileMutation.error}
          variant="banner"
          size="sm"
        />
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          {t('buttons.cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? t('buttons.saving') : t('buttons.saveAndContinue')}
        </Button>
      </div>
    </form>
  );
}
