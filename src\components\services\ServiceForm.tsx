import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useCreateService, useUpdateService, useServiceCategories } from '../../hooks/useServices';
import { Service, ServiceCreateRequest } from '../../types';
import { useCommonTranslation, useFormTranslation, useManagementTranslation } from '../../hooks/useTranslation';

// Validation schema will be created inside component to use translations

type ServiceFormData = {
  title: string;
  description?: string;
  duration: number;
  price: number;
  pointsRequirements: number;
  deliveryType: 'at_location' | 'at_customer' | 'both';
  color: string;
  serviceCategoryId?: number;
  isPublic: boolean;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  servedRegions?: string;
};

interface ServiceFormProps {
  service?: Service | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Color options will be updated with translations inside the component

export default function ServiceForm({ service, onClose, onSuccess }: ServiceFormProps) {
  const isEditing = !!service;
  const { data: categories } = useServiceCategories();
  const createServiceMutation = useCreateService();
  const updateServiceMutation = useUpdateService();
  const { t } = useCommonTranslation();
  const { t: tForm } = useFormTranslation();
  const { t: tManagement, currentLanguage } = useManagementTranslation();

  // Custom translations for service form
  const formTranslations = {
    ar: {
      createNewService: "إنشاء خدمة جديدة",
      editService: "تعديل الخدمة",
      addNewServiceDescription: "أضف خدمة جديدة إلى عروضك",
      updateServiceDescription: "تحديث تفاصيل الخدمة",
      serviceName: "اسم الخدمة",
      duration: "المدة (بالدقائق)",
      price: "السعر",
      pointsRequired: "النقاط المطلوبة",
      deliveryType: "نوع التسليم",
      category: "الفئة",
      color: "اللون",
      description: "الوصف",
      servedRegions: "المناطق المخدومة",
      serviceSettings: "إعدادات الخدمة",
      serviceNamePlaceholder: "أدخل اسم الخدمة",
      durationPlaceholder: "60",
      pricePlaceholder: "0.00",
      pointsPlaceholder: "0",
      descriptionPlaceholder: "اصف خدمتك...",
      regionsPlaceholder: "أدخل المناطق مفصولة بفواصل (مثال: وسط المدينة، الضواحي)",
      atLocation: "في الموقع",
      atCustomer: "عند العميل",
      both: "كلاهما",
      noCategory: "بدون فئة",
      publicService: "خدمة عامة",
      publicServiceDesc: "مرئية للعملاء",
      onlineBooking: "الحجز عبر الإنترنت",
      onlineBookingDesc: "السماح بالمواعيد عبر الإنترنت",
      acceptNewCustomers: "قبول عملاء جدد",
      acceptNewCustomersDesc: "مفتوح للعملاء الجدد",
      notifications: "الإشعارات",
      notificationsDesc: "تلقي تنبيهات الحجز",
      pointsTooltip: "النقاط/الائتمانات التي يحتاجها العملاء لحجز هذه الخدمة. الافتراضي نقطة واحدة لكل حجز.",
      regionsHelp: "اتركه فارغاً إذا كانت الخدمة متاحة في كل مكان",
      createService: "إنشاء خدمة",
      updateService: "تحديث الخدمة"
    },
    en: {
      createNewService: "Create New Service",
      editService: "Edit Service",
      addNewServiceDescription: "Add a new service to your offerings",
      updateServiceDescription: "Update service details",
      serviceName: "Service Name",
      duration: "Duration (minutes)",
      price: "Price",
      pointsRequired: "Points Required",
      deliveryType: "Delivery Type",
      category: "Category",
      color: "Color",
      description: "Description",
      servedRegions: "Served Regions",
      serviceSettings: "Service Settings",
      serviceNamePlaceholder: "Enter service name",
      durationPlaceholder: "60",
      pricePlaceholder: "0.00",
      pointsPlaceholder: "0",
      descriptionPlaceholder: "Describe your service...",
      regionsPlaceholder: "Enter regions separated by commas (e.g., Downtown, Uptown, Suburbs)",
      atLocation: "At Location",
      atCustomer: "At Customer",
      both: "Both",
      noCategory: "No Category",
      publicService: "Public Service",
      publicServiceDesc: "Visible to customers",
      onlineBooking: "Online Booking",
      onlineBookingDesc: "Allow online appointments",
      acceptNewCustomers: "Accept New Customers",
      acceptNewCustomersDesc: "Open to new clients",
      notifications: "Notifications",
      notificationsDesc: "Receive booking alerts",
      pointsTooltip: "Credits/points customers need to book this service. Default is 1 point per booking.",
      regionsHelp: "Leave empty if service is available everywhere",
      createService: "Create Service",
      updateService: "Update Service"
    },
    fr: {
      createNewService: "Créer un nouveau service",
      editService: "Modifier le service",
      addNewServiceDescription: "Ajoutez un nouveau service à vos offres",
      updateServiceDescription: "Mettre à jour les détails du service",
      serviceName: "Nom du service",
      duration: "Durée (minutes)",
      price: "Prix",
      pointsRequired: "Points requis",
      deliveryType: "Type de livraison",
      category: "Catégorie",
      color: "Couleur",
      description: "Description",
      servedRegions: "Régions desservies",
      serviceSettings: "Paramètres du service",
      serviceNamePlaceholder: "Entrez le nom du service",
      durationPlaceholder: "60",
      pricePlaceholder: "0,00",
      pointsPlaceholder: "0",
      descriptionPlaceholder: "Décrivez votre service...",
      regionsPlaceholder: "Entrez les régions séparées par des virgules (ex: Centre-ville, Banlieue)",
      atLocation: "Sur place",
      atCustomer: "Chez le client",
      both: "Les deux",
      noCategory: "Aucune catégorie",
      publicService: "Service public",
      publicServiceDesc: "Visible par les clients",
      onlineBooking: "Réservation en ligne",
      onlineBookingDesc: "Autoriser les rendez-vous en ligne",
      acceptNewCustomers: "Accepter de nouveaux clients",
      acceptNewCustomersDesc: "Ouvert aux nouveaux clients",
      notifications: "Notifications",
      notificationsDesc: "Recevoir les alertes de réservation",
      pointsTooltip: "Crédits/points dont les clients ont besoin pour réserver ce service. Par défaut, 1 point par réservation.",
      regionsHelp: "Laissez vide si le service est disponible partout",
      createService: "Créer un service",
      updateService: "Mettre à jour le service"
    }
  };

  const currentLang = currentLanguage as keyof typeof formTranslations;
  const ft = (key: keyof typeof formTranslations.ar) =>
    formTranslations[currentLang]?.[key] || formTranslations.en[key] || key;

  // Color options with translations
  const colorTranslations = {
    ar: { blue: "أزرق", green: "أخضر", yellow: "أصفر", red: "أحمر", purple: "بنفسجي", cyan: "سماوي", orange: "برتقالي", lime: "ليموني" },
    en: { blue: "Blue", green: "Green", yellow: "Yellow", red: "Red", purple: "Purple", cyan: "Cyan", orange: "Orange", lime: "Lime" },
    fr: { blue: "Bleu", green: "Vert", yellow: "Jaune", red: "Rouge", purple: "Violet", cyan: "Cyan", orange: "Orange", lime: "Citron vert" }
  };

  const ct = (key: keyof typeof colorTranslations.ar) =>
    colorTranslations[currentLang]?.[key] || colorTranslations.en[key] || key;

  const colorOptions = [
    { value: '#3B82F6', label: ct('blue') },
    { value: '#10B981', label: ct('green') },
    { value: '#F59E0B', label: ct('yellow') },
    { value: '#EF4444', label: ct('red') },
    { value: '#8B5CF6', label: ct('purple') },
    { value: '#06B6D4', label: ct('cyan') },
    { value: '#F97316', label: ct('orange') },
    { value: '#84CC16', label: ct('lime') },
  ];

  // Validation schema using translations
  const serviceSchema = z.object({
    title: z.string().min(1, tForm('validation.required')).max(255, tForm('validation.maxLength', { max: 255 })),
    description: z.string().max(1000, tForm('validation.maxLength', { max: 1000 })).optional(),
    duration: z.number().int().min(1, tForm('validation.minValue', { min: 1 })).max(1440, tForm('validation.maxValue', { max: 1440 })),
    price: z.number().min(0, tForm('validation.minValue', { min: 0 })).max(999999.99, tForm('validation.maxValue', { max: 999999.99 })),
    pointsRequirements: z.number().int().min(0, tForm('validation.minValue', { min: 0 })).default(1),
    deliveryType: z.enum(['at_location', 'at_customer', 'both']),
    color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, tForm('validation.invalidFormat')),
    serviceCategoryId: z.union([z.number(), z.undefined()]).optional(),
    isPublic: z.boolean().default(true),
    acceptOnline: z.boolean().default(true),
    acceptNew: z.boolean().default(true),
    notificationOn: z.boolean().default(true),
    servedRegions: z.string().optional(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      title: '',
      description: '',
      duration: 30,
      price: 0,
      pointsRequirements: 1,
      deliveryType: 'at_location',
      color: '#4CAF50',
      serviceCategoryId: undefined,
      isPublic: true,
      acceptOnline: true,
      acceptNew: true,
      notificationOn: true,
      servedRegions: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (service) {
      reset({
        title: service.title,
        description: service.description || '',
        duration: service.duration,
        price: service.price,
        pointsRequirements: service.pointsRequirements,
        deliveryType: service.deliveryType,
        color: service.color,
        serviceCategoryId: service.serviceCategoryId || undefined,
        isPublic: service.isPublic,
        acceptOnline: service.acceptOnline,
        acceptNew: service.acceptNew,
        notificationOn: service.notificationOn,
        servedRegions: service.servedRegions?.join(', ') || '',
      });
    }
  }, [service, reset]);

  const onSubmit = async (data: ServiceFormData) => {
    console.log('Form submitted with data:', data);
    try {
      // Validate served regions for customer delivery
      if ((data.deliveryType === 'at_customer' || data.deliveryType === 'both') &&
          (!data.servedRegions || data.servedRegions.trim() === '')) {
        toast.error('Served regions are required for customer location services');
        return;
      }

      const serviceData: ServiceCreateRequest = {
        ...data,
        servedRegions: data.servedRegions
          ? data.servedRegions.split(',').map(region => region.trim()).filter(Boolean)
          : [],
      };

      console.log('Submitting service data:', serviceData);

      if (isEditing && service) {
        console.log('Updating service...');
        await updateServiceMutation.mutateAsync({
          id: service.id,
          data: serviceData,
        });
      } else {
        console.log('Creating new service...');
        await createServiceMutation.mutateAsync(serviceData);
      }

      console.log('Service operation successful, calling onSuccess');
      onSuccess();
    } catch (error) {
      console.error('Error in form submission:', error);
      // Error handled by mutations
    }
  };

  const isLoading = createServiceMutation.isPending || updateServiceMutation.isPending;
  const error = createServiceMutation.error || updateServiceMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? ft('editService') : ft('createNewService')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? ft('updateServiceDescription') : ft('addNewServiceDescription')}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit, (errors) => {
        console.log('Form validation errors:', errors);
      })} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label>
                  {ft('serviceName')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('title')}
                  placeholder={ft('serviceNamePlaceholder')}
                  disabled={isLoading}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  {ft('duration')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('duration', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  placeholder={ft('durationPlaceholder')}
                  disabled={isLoading}
                />
                {errors.duration && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.duration.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  {ft('price')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('price', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder={ft('pricePlaceholder')}
                  disabled={isLoading}
                />
                {errors.price && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.price.message}
                  </p>
                )}
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Label>{ft('pointsRequired')}</Label>
                  <button
                    type="button"
                    className="w-4 h-4 rounded-full bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-500 flex items-center justify-center text-xs font-medium transition-colors"
                    title={ft('pointsTooltip')}
                  >
                    ?
                  </button>
                </div>
                <Input
                  {...register('pointsRequirements', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  placeholder={ft('pointsPlaceholder')}
                  disabled={isLoading}
                />
                {errors.pointsRequirements && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.pointsRequirements.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  {ft('deliveryType')} <span className="text-red-500">*</span>
                </Label>
                <select
                  {...register('deliveryType')}
                  disabled={isLoading}
                  className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                >
                  <option value="at_location">{ft('atLocation')}</option>
                  <option value="at_customer">{ft('atCustomer')}</option>
                  <option value="both">{ft('both')}</option>
                </select>
                {errors.deliveryType && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.deliveryType.message}
                  </p>
                )}
              </div>
            </div>

            {/* Category and Color */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label>{ft('category')}</Label>
                <select
                  {...register('serviceCategoryId', {
                    setValueAs: (value) => value === '' ? undefined : Number(value)
                  })}
                  disabled={isLoading}
                  className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                >
                  <option value="">{ft('noCategory')}</option>
                  {categories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label>
                  {ft('color')} <span className="text-red-500">*</span>
                </Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {colorOptions.map((color) => (
                    <label
                      key={color.value}
                      className="flex items-center cursor-pointer"
                    >
                      <input
                        {...register('color')}
                        type="radio"
                        value={color.value}
                        className="sr-only"
                        disabled={isLoading}
                      />
                      <div
                        className={`w-8 h-8 rounded-full border-2 ${
                          watch('color') === color.value
                            ? 'border-gray-900 dark:border-white'
                            : 'border-gray-300 dark:border-gray-600'
                        }`}
                        style={{ backgroundColor: color.value }}
                        title={color.label}
                      />
                    </label>
                  ))}
                </div>
                {errors.color && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.color.message}
                  </p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <Label>{ft('description')}</Label>
              <textarea
                {...register('description')}
                rows={3}
                placeholder={ft('descriptionPlaceholder')}
                disabled={isLoading}
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
              />
            </div>

            {/* Served Regions */}
            <div>
              <Label>{ft('servedRegions')}</Label>
              <Input
                {...register('servedRegions')}
                placeholder={ft('regionsPlaceholder')}
                disabled={isLoading}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {ft('regionsHelp')}
              </p>
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-800 dark:text-white">
                {ft('serviceSettings')}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('isPublic')}
                    onChange={(checked) => setValue('isPublic', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('publicService')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('publicServiceDesc')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('acceptOnline')}
                    onChange={(checked) => setValue('acceptOnline', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('onlineBooking')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('onlineBookingDesc')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('acceptNew')}
                    onChange={(checked) => setValue('acceptNew', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('acceptNewCustomers')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('acceptNewCustomersDesc')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('notificationOn')}
                    onChange={(checked) => setValue('notificationOn', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('notifications')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('notificationsDesc')}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {error && (
              <ErrorDisplay
                error={error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {t('actions.cancel')}
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading
                  ? (isEditing ? t('messages.updating') : t('messages.saving'))
                  : (isEditing ? ft('updateService') : ft('createService'))
                }
              </Button>
            </div>
      </form>
    </div>
  );
}
