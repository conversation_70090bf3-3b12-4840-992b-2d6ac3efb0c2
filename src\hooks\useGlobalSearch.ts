import { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useLanguage } from '../context/LanguageContext';
import { LanguageCode } from '../context/LanguageContext';

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  path: string;
  category: 'page' | 'feature' | 'action';
  icon?: string;
  keywords?: string[];
  matchedLanguage?: LanguageCode; // Language that matched the search query
}

interface MultilingualSearchItem {
  id: string;
  path: string;
  category: 'page' | 'feature' | 'action';
  icon?: string;
  translations: {
    [key in LanguageCode]: {
      title: string;
      description: string;
      keywords?: string[];
    };
  };
}

// Define all searchable pages and features with multilingual support
const multilingualSearchItems: MultilingualSearchItem[] = [
  // Main Pages
  {
    id: 'dashboard',
    path: '/',
    category: 'page',
    icon: '📊',
    translations: {
      en: {
        title: 'Dashboard',
        description: 'Overview of your business metrics and activities',
        keywords: ['home', 'overview', 'metrics', 'analytics', 'stats']
      },
      ar: {
        title: 'لوحة التحكم',
        description: 'نظرة عامة على مقاييس أعمالك وأنشطتك',
        keywords: ['الرئيسية', 'نظرة عامة', 'مقاييس', 'تحليلات', 'إحصائيات']
      },
      fr: {
        title: 'Tableau de bord',
        description: 'Aperçu de vos métriques et activités commerciales',
        keywords: ['accueil', 'aperçu', 'métriques', 'analytique', 'statistiques']
      }
    }
  },
  {
    id: 'calendar',
    path: '/calendar',
    category: 'page',
    icon: '📅',
    translations: {
      en: {
        title: 'Calendar',
        description: 'View and manage appointments in calendar format',
        keywords: ['schedule', 'appointments', 'booking', 'time', 'date']
      },
      ar: {
        title: 'التقويم',
        description: 'عرض وإدارة المواعيد في تنسيق التقويم',
        keywords: ['جدولة', 'مواعيد', 'حجز', 'وقت', 'تاريخ']
      },
      fr: {
        title: 'Calendrier',
        description: 'Voir et gérer les rendez-vous au format calendrier',
        keywords: ['planification', 'rendez-vous', 'réservation', 'temps', 'date']
      }
    }
  },
  {
    id: 'appointments',
    path: '/appointments',
    category: 'page',
    icon: '📋',
    translations: {
      en: {
        title: 'Appointments',
        description: 'Manage all your appointments and bookings',
        keywords: ['bookings', 'schedule', 'clients', 'meetings']
      },
      ar: {
        title: 'المواعيد',
        description: 'إدارة جميع مواعيدك وحجوزاتك',
        keywords: ['حجوزات', 'جدولة', 'عملاء', 'اجتماعات']
      },
      fr: {
        title: 'Rendez-vous',
        description: 'Gérer tous vos rendez-vous et réservations',
        keywords: ['réservations', 'planification', 'clients', 'réunions']
      }
    }
  },
  {
    id: 'customers',
    path: '/customers',
    category: 'page',
    icon: '👥',
    translations: {
      en: {
        title: 'Customers',
        description: 'Manage your customer database and relationships',
        keywords: ['clients', 'contacts', 'crm', 'people', 'database']
      },
      ar: {
        title: 'العملاء',
        description: 'إدارة قاعدة بيانات العملاء والعلاقات',
        keywords: ['عملاء', 'جهات اتصال', 'إدارة علاقات', 'أشخاص', 'قاعدة بيانات']
      },
      fr: {
        title: 'Clients',
        description: 'Gérer votre base de données clients et relations',
        keywords: ['clients', 'contacts', 'crm', 'personnes', 'base de données']
      }
    }
  },
  {
    id: 'services',
    path: '/services',
    category: 'page',
    icon: '⚙️',
    translations: {
      en: {
        title: 'Services',
        description: 'Configure your services, pricing, and offerings',
        keywords: ['offerings', 'pricing', 'setup', 'configuration', 'products']
      },
      ar: {
        title: 'الخدمات',
        description: 'تكوين خدماتك والتسعير والعروض',
        keywords: ['عروض', 'تسعير', 'إعداد', 'تكوين', 'منتجات']
      },
      fr: {
        title: 'Services',
        description: 'Configurer vos services, tarifs et offres',
        keywords: ['offres', 'tarification', 'configuration', 'produits']
      }
    }
  },
  {
    id: 'locations',
    path: '/locations',
    category: 'page',
    icon: '📍',
    translations: {
      en: {
        title: 'Locations',
        description: 'Manage your business locations and addresses',
        keywords: ['addresses', 'places', 'venues', 'setup', 'branches']
      },
      ar: {
        title: 'المواقع',
        description: 'إدارة مواقع أعمالك والعناوين',
        keywords: ['عناوين', 'أماكن', 'مواقع', 'إعداد', 'فروع']
      },
      fr: {
        title: 'Emplacements',
        description: 'Gérer vos emplacements et adresses commerciales',
        keywords: ['adresses', 'lieux', 'emplacements', 'configuration', 'succursales']
      }
    }
  },
  {
    id: 'queues',
    path: '/queues',
    category: 'page',
    icon: '🚶',
    translations: {
      en: {
        title: 'Queues',
        description: 'Manage waiting queues and walk-in customers',
        keywords: ['waiting', 'walk-in', 'line', 'queue management']
      },
      ar: {
        title: 'طوابير الانتظار',
        description: 'إدارة طوابير الانتظار والعملاء المباشرين',
        keywords: ['انتظار', 'مباشر', 'طابور', 'إدارة طوابير']
      },
      fr: {
        title: 'Files d\'attente',
        description: 'Gérer les files d\'attente et clients sans rendez-vous',
        keywords: ['attente', 'sans rendez-vous', 'file', 'gestion files']
      }
    }
  },
  {
    id: 'profile',
    path: '/profile',
    category: 'page',
    icon: '👤',
    translations: {
      en: {
        title: 'Profile',
        description: 'Manage your provider profile and settings',
        keywords: ['account', 'settings', 'personal', 'information', 'details']
      },
      ar: {
        title: 'الملف الشخصي',
        description: 'إدارة ملف مقدم الخدمة والإعدادات',
        keywords: ['حساب', 'إعدادات', 'شخصي', 'معلومات', 'تفاصيل']
      },
      fr: {
        title: 'Profil',
        description: 'Gérer votre profil de prestataire et paramètres',
        keywords: ['compte', 'paramètres', 'personnel', 'informations', 'détails']
      }
    }
  },
  {
    id: 'advanced',
    path: '/advanced',
    category: 'page',
    icon: '🔧',
    translations: {
      en: {
        title: 'Advanced Features',
        description: 'Access advanced tools and features',
        keywords: ['tools', 'advanced', 'features', 'settings']
      },
      ar: {
        title: 'الميزات المتقدمة',
        description: 'الوصول إلى الأدوات والميزات المتقدمة',
        keywords: ['أدوات', 'متقدم', 'ميزات', 'إعدادات']
      },
      fr: {
        title: 'Fonctionnalités avancées',
        description: 'Accéder aux outils et fonctionnalités avancés',
        keywords: ['outils', 'avancé', 'fonctionnalités', 'paramètres']
      }
    }
  },

  // Quick Actions
  {
    id: 'new-appointment',
    path: '/calendar',
    category: 'action',
    icon: '➕',
    translations: {
      en: {
        title: 'New Appointment',
        description: 'Create a new appointment booking',
        keywords: ['create', 'book', 'schedule', 'add', 'new booking']
      },
      ar: {
        title: 'موعد جديد',
        description: 'إنشاء حجز موعد جديد',
        keywords: ['إنشاء', 'حجز', 'جدولة', 'إضافة', 'حجز جديد']
      },
      fr: {
        title: 'Nouveau rendez-vous',
        description: 'Créer une nouvelle réservation de rendez-vous',
        keywords: ['créer', 'réserver', 'planifier', 'ajouter', 'nouvelle réservation']
      }
    }
  },
  {
    id: 'add-customer',
    path: '/customers',
    category: 'action',
    icon: '👤',
    translations: {
      en: {
        title: 'Add Customer',
        description: 'Add a new customer to your database',
        keywords: ['create', 'new client', 'add contact', 'register']
      },
      ar: {
        title: 'إضافة عميل',
        description: 'إضافة عميل جديد إلى قاعدة البيانات',
        keywords: ['إنشاء', 'عميل جديد', 'إضافة جهة اتصال', 'تسجيل']
      },
      fr: {
        title: 'Ajouter un client',
        description: 'Ajouter un nouveau client à votre base de données',
        keywords: ['créer', 'nouveau client', 'ajouter contact', 'enregistrer']
      }
    }
  },
  {
    id: 'add-service',
    path: '/services',
    category: 'action',
    icon: '⚙️',
    translations: {
      en: {
        title: 'Add Service',
        description: 'Create a new service offering',
        keywords: ['create', 'new service', 'offering', 'setup']
      },
      ar: {
        title: 'إضافة خدمة',
        description: 'إنشاء عرض خدمة جديد',
        keywords: ['إنشاء', 'خدمة جديدة', 'عرض', 'إعداد']
      },
      fr: {
        title: 'Ajouter un service',
        description: 'Créer une nouvelle offre de service',
        keywords: ['créer', 'nouveau service', 'offre', 'configuration']
      }
    }
  },
  {
    id: 'add-location',
    path: '/locations',
    category: 'action',
    icon: '📍',
    translations: {
      en: {
        title: 'Add Location',
        description: 'Add a new business location',
        keywords: ['create', 'new location', 'address', 'venue']
      },
      ar: {
        title: 'إضافة موقع',
        description: 'إضافة موقع عمل جديد',
        keywords: ['إنشاء', 'موقع جديد', 'عنوان', 'مكان']
      },
      fr: {
        title: 'Ajouter un emplacement',
        description: 'Ajouter un nouvel emplacement commercial',
        keywords: ['créer', 'nouvel emplacement', 'adresse', 'lieu']
      }
    }
  },

  // Additional Features
  {
    id: 'settings',
    path: '/profile',
    category: 'feature',
    icon: '⚙️',
    translations: {
      en: {
        title: 'Settings',
        description: 'Configure your account and application settings',
        keywords: ['configuration', 'preferences', 'account', 'setup']
      },
      ar: {
        title: 'الإعدادات',
        description: 'تكوين إعدادات حسابك والتطبيق',
        keywords: ['تكوين', 'تفضيلات', 'حساب', 'إعداد']
      },
      fr: {
        title: 'Paramètres',
        description: 'Configurer vos paramètres de compte et d\'application',
        keywords: ['configuration', 'préférences', 'compte', 'configuration']
      }
    }
  },
  {
    id: 'reports',
    path: '/',
    category: 'feature',
    icon: '📊',
    translations: {
      en: {
        title: 'Reports & Analytics',
        description: 'View business reports and analytics',
        keywords: ['analytics', 'statistics', 'data', 'insights', 'metrics']
      },
      ar: {
        title: 'التقارير والتحليلات',
        description: 'عرض تقارير الأعمال والتحليلات',
        keywords: ['تحليلات', 'إحصائيات', 'بيانات', 'رؤى', 'مقاييس']
      },
      fr: {
        title: 'Rapports et analyses',
        description: 'Voir les rapports commerciaux et analyses',
        keywords: ['analyses', 'statistiques', 'données', 'insights', 'métriques']
      }
    }
  },
  {
    id: 'help',
    path: '/advanced',
    category: 'feature',
    icon: '❓',
    translations: {
      en: {
        title: 'Help & Support',
        description: 'Get help and support documentation',
        keywords: ['support', 'documentation', 'faq', 'assistance', 'guide']
      },
      ar: {
        title: 'المساعدة والدعم',
        description: 'الحصول على المساعدة ووثائق الدعم',
        keywords: ['دعم', 'وثائق', 'أسئلة شائعة', 'مساعدة', 'دليل']
      },
      fr: {
        title: 'Aide et support',
        description: 'Obtenir de l\'aide et la documentation de support',
        keywords: ['support', 'documentation', 'faq', 'assistance', 'guide']
      }
    }
  },
  {
    id: 'service-session',
    path: '/appointments',
    category: 'feature',
    icon: '⏱️',
    translations: {
      en: {
        title: 'Service Session',
        description: 'Active appointment session management with timer',
        keywords: ['session', 'timer', 'active', 'in progress', 'current appointment']
      },
      ar: {
        title: 'جلسة الخدمة',
        description: 'إدارة جلسة الموعد النشطة مع المؤقت',
        keywords: ['جلسة', 'مؤقت', 'نشط', 'قيد التقدم', 'موعد حالي']
      },
      fr: {
        title: 'Session de service',
        description: 'Gestion de session de rendez-vous active avec minuteur',
        keywords: ['session', 'minuteur', 'actif', 'en cours', 'rendez-vous actuel']
      }
    }
  }
];

const RECENT_SEARCHES_KEY = 'dalti-recent-searches';
const MAX_RECENT_SEARCHES = 5;

export const useGlobalSearch = () => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([]);
  const navigate = useNavigate();
  const { currentLanguage } = useLanguage();

  // Convert multilingual search items to SearchResult format for display (using current language)
  const getDisplaySearchResult = useCallback((item: MultilingualSearchItem): SearchResult => {
    const translation = item.translations[currentLanguage];
    return {
      id: item.id,
      title: translation.title,
      description: translation.description,
      path: item.path,
      category: item.category,
      icon: item.icon,
      keywords: translation.keywords || []
    };
  }, [currentLanguage]);

  // Load recent searches from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setRecentSearches(parsed);
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearches = useCallback((searches: SearchResult[]) => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(searches));
      setRecentSearches(searches);
    } catch (error) {
      console.warn('Failed to save recent searches:', error);
    }
  }, []);

  // Add to recent searches
  const addToRecentSearches = useCallback((result: SearchResult) => {
    setRecentSearches(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item.id !== result.id);
      // Add to beginning
      const updated = [result, ...filtered].slice(0, MAX_RECENT_SEARCHES);
      saveRecentSearches(updated);
      return updated;
    });
  }, [saveRecentSearches]);

  // Normalize text for better search matching (handles Arabic, French accents, etc.)
  const normalizeText = useCallback((text: string): string => {
    return text
      .toLowerCase()
      .trim()
      // Remove diacritics and normalize Unicode
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      // Handle Arabic text normalization
      .replace(/[أإآ]/g, 'ا')
      .replace(/[ة]/g, 'ه')
      .replace(/[ى]/g, 'ي');
  }, []);

  // Search across all languages and return results in current language format
  const searchResults = useMemo(() => {
    const searchTerm = normalizeText(query);

    // If no query, show recent searches
    if (!searchTerm) {
      return recentSearches;
    }

    return multilingualSearchItems
      .map(item => {
        let score = 0;
        let matchedLanguage: LanguageCode | null = null;

        // Search across all language translations
        for (const [langCode, translation] of Object.entries(item.translations)) {
          const lang = langCode as LanguageCode;
          const normalizedTitle = normalizeText(translation.title);
          const normalizedDescription = normalizeText(translation.description);

          // Exact title match gets highest score
          if (normalizedTitle === searchTerm) {
            score = Math.max(score, 100);
            matchedLanguage = lang;
          }
          // Title starts with search term
          else if (normalizedTitle.startsWith(searchTerm)) {
            score = Math.max(score, 80);
            matchedLanguage = lang;
          }
          // Title contains search term
          else if (normalizedTitle.includes(searchTerm)) {
            score = Math.max(score, 60);
            matchedLanguage = lang;
          }

          // Description contains search term
          if (normalizedDescription.includes(searchTerm)) {
            score = Math.max(score, 30);
            if (!matchedLanguage) matchedLanguage = lang;
          }

          // Keywords match
          if (translation.keywords) {
            for (const keyword of translation.keywords) {
              const normalizedKeyword = normalizeText(keyword);
              if (normalizedKeyword.includes(searchTerm)) {
                score = Math.max(score, 20);
                if (!matchedLanguage) matchedLanguage = lang;
              }
              if (normalizedKeyword === searchTerm) {
                score = Math.max(score, 40);
                matchedLanguage = lang;
              }
            }
          }
        }

        // Return the search result in the current language format for display
        const displayResult = getDisplaySearchResult(item);
        return { ...displayResult, score, matchedLanguage };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, 8); // Limit to top 8 results
  }, [query, recentSearches, normalizeText, getDisplaySearchResult]);

  const handleSearch = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
    setIsOpen(searchQuery.length > 0);
  }, []);

  const handleSelectResult = useCallback((result: SearchResult) => {
    addToRecentSearches(result);
    navigate(result.path);
    setQuery('');
    setIsOpen(false);
  }, [navigate, addToRecentSearches]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const clearSearch = useCallback(() => {
    setQuery('');
    setIsOpen(false);
  }, []);

  const clearRecentSearches = useCallback(() => {
    saveRecentSearches([]);
  }, [saveRecentSearches]);

  return {
    query,
    searchResults,
    isOpen,
    recentSearches,
    handleSearch,
    handleSelectResult,
    handleClose,
    clearSearch,
    clearRecentSearches
  };
};
