import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UserService, ProfilePictureResponse } from '../services/user.service';
import { ErrorLogger } from '../lib/error-utils';
import { performS3Upload, UploadOptions } from '../utils/s3-upload.utils';
import { LanguageCode } from '../context/LanguageContext';
import toast from 'react-hot-toast';

/**
 * Hook for fetching user's profile picture
 */
export const useGetProfilePicture = () => {
  return useQuery<ProfilePictureResponse>({
    queryKey: ['user', 'profilePicture'],
    queryFn: () => UserService.getProfilePicture(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook for uploading user's profile picture
 */
export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (options: Omit<UploadOptions, 'validateFile'>) => {
      return performS3Upload(
        (fileName, fileType) => UserService.generateProfilePictureUploadUrl(fileName, fileType),
        { ...options, validateFile: true }
      );
    },
    onSuccess: (result) => {
      // Invalidate profile picture queries to refetch with new image
      queryClient.invalidateQueries({ queryKey: ['user', 'profilePicture'] });
      
      // Also invalidate auth context if it caches user data
      queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
      
      toast.success('Profile picture uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to upload profile picture';
      ErrorLogger.log(error, { context: 'uploadProfilePicture' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting user's profile picture
 */
export const useDeleteProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => UserService.deleteProfilePicture(),
    onSuccess: () => {
      // Invalidate profile picture queries to refetch without image
      queryClient.invalidateQueries({ queryKey: ['user', 'profilePicture'] });
      
      // Also invalidate auth context if it caches user data
      queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
      
      toast.success('Profile picture removed successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to remove profile picture';
      ErrorLogger.log(error, { context: 'deleteProfilePicture' });
      toast.error(message);
    },
  });
};

/**
 * Hook to check if user has a profile picture
 */
export const useHasProfilePicture = () => {
  const { data: profilePicture, isLoading } = useGetProfilePicture();
  
  return {
    hasProfilePicture: profilePicture?.hasProfilePicture || false,
    profilePictureUrl: profilePicture?.profilePicture?.downloadUrl || null,
    isLoading,
  };
};

/**
 * Hook for updating user's preferred language
 */
export const useUpdatePreferredLanguage = () => {
  return useMutation({
    mutationFn: (languageCode: LanguageCode) => UserService.updatePreferredLanguage(languageCode),
    onSuccess: (_, languageCode) => {
      console.log(`✅ Preferred language updated to: ${languageCode}`);
      // Note: We don't show a toast here as it would be annoying during language switching
      // The language change itself provides visual feedback
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update preferred language';
      ErrorLogger.log(error, {
        context: 'updatePreferredLanguage',
        languageCode: error?.languageCode
      });
      console.error('❌ Failed to update preferred language:', message);
      // Only show error toast, not success to avoid interrupting UX
      toast.error('Failed to save language preference');
    },
  });
};

/**
 * Combined hook for profile picture management
 */
export const useProfilePictureManager = () => {
  const profilePictureQuery = useGetProfilePicture();
  const uploadMutation = useUploadProfilePicture();
  const deleteMutation = useDeleteProfilePicture();

  return {
    // Data
    profilePicture: profilePictureQuery.data,
    hasProfilePicture: profilePictureQuery.data?.hasProfilePicture || false,
    profilePictureUrl: profilePictureQuery.data?.profilePicture?.downloadUrl || null,

    // Loading states
    isLoading: profilePictureQuery.isLoading,
    isUploading: uploadMutation.isPending,
    isDeleting: deleteMutation.isPending,

    // Actions
    uploadProfilePicture: uploadMutation.mutateAsync,
    deleteProfilePicture: deleteMutation.mutateAsync,

    // Utilities
    refetch: profilePictureQuery.refetch,

    // Error states
    error: profilePictureQuery.error || uploadMutation.error || deleteMutation.error,
  };
};
