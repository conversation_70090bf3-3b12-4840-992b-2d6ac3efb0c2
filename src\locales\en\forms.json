{"labels": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "mobile": "Mobile Number", "address": "Address", "city": "City", "state": "State", "country": "Country", "zipCode": "ZIP Code", "name": "Name", "title": "Title", "description": "Description", "notes": "Notes", "price": "Price", "duration": "Duration", "category": "Category", "status": "Status", "date": "Date", "time": "Time", "startTime": "Start Time", "endTime": "End Time", "timezone": "Timezone", "location": "Location", "service": "Service", "customer": "Customer", "provider": "Provider", "businessName": "Business Name", "website": "Website", "socialMedia": "Social Media", "specialization": "Specialization", "experience": "Experience", "education": "Education", "certifications": "Certifications", "languages": "Languages", "availability": "Availability", "workingHours": "Working Hours", "breakTime": "Break Time", "holidays": "Holidays", "emergencyContact": "Emergency Contact", "insurance": "Insurance", "licenseNumber": "License Number", "taxId": "Tax ID", "bankAccount": "Bank Account", "paymentMethods": "Payment Methods", "basicInformation": "Basic Information", "shortName": "Short Name", "businessLogo": "Business Logo", "businessDescription": "Business Description", "businessPresentation": "Business Presentation", "addYourBusinessLocations": "Add Your Business Locations", "yourLocations": "Your Locations", "addLocation": "Add Location", "addYourFirstLocation": "Add Your First Location", "addYourServices": "Add Your Services", "yourServices": "Your Services", "addService": "Add Service", "addYourFirstService": "Add Your First Service", "atLocation": "At Location", "atCustomer": "At Customer", "both": "Both", "onlineBooking": "Online Booking", "notifications": "Notifications", "regions": "Regions", "active": "Active", "private": "Private", "notAccepting": "Not Accepting", "pointsRequired": "Points Required", "delivery": "Delivery", "setUpAppointmentQueues": "Set Up Your Appointment Queues", "yourQueues": "Your Queues", "addAnotherQueue": "Add Another Queue", "createYourFirstQueue": "Create Your First Queue", "noQueuesYet": "No Queues Yet"}, "placeholders": {"enterFirstName": "Enter first name", "enterLastName": "Enter last name", "enterEmail": "Enter email address", "enterPhone": "Enter phone number", "enterAddress": "Enter address", "enterCity": "Enter city", "selectCountry": "Select country", "enterZipCode": "Enter ZIP code", "enterName": "Enter name", "enterTitle": "Enter title", "enterDescription": "Enter description", "enterNotes": "Enter notes", "enterPrice": "Enter price", "selectDuration": "Select duration", "selectCategory": "Select category", "selectStatus": "Select status", "selectDate": "Select date", "selectTime": "Select time", "selectTimezone": "Select timezone", "selectLocation": "Select location", "selectService": "Select service", "selectCustomer": "Select customer", "enterBusinessName": "Enter business name", "enterPhoneNumber": "Enter your phone number", "tellCustomersAboutBusiness": "Tell customers about your business...", "uploadBusinessLogo": "Upload your business logo to make your profile more professional", "clickToUploadOrDrag": "Click to upload or drag and drop", "fileTypesAndSize": "PNG, JPG up to 10 MB", "locationsHelpText": "Add the locations where you provide services. This helps customers find and book appointments with you.", "addLocationHelpText": "Add your business location so customers know where to find you.", "locationsConfiguredText": "Great! You have {count} location{plural} configured", "addMoreLocationsText": "You can add more locations anytime from your dashboard.", "servicesHelpText": "Add the services you offer to your customers. You can always add more services later.", "addServiceHelpText": "Add your first service to get started with accepting appointments.", "servicesConfiguredText": "Great! You have {count} service{plural} configured", "addMoreServicesText": "You can add more services anytime from your dashboard.", "queuesHelpText": "Create queues to manage customer appointments and wait times. Each queue can be assigned to specific services and locations.", "addQueueHelpText": "Create your first appointment queue to start managing customer wait times.", "enterWebsite": "Enter website URL", "enterSocialMedia": "Enter social media handle", "selectSpecialization": "Select specialization", "enterExperience": "Enter years of experience", "enterEducation": "Enter education details", "enterCertifications": "Enter certifications", "selectLanguages": "Select languages", "searchCustomers": "Search customers", "searchServices": "Search services", "searchLocations": "Search locations", "enterShortName": "Enter short name"}, "validation": {"required": "This field is required", "emailInvalid": "Please enter a valid email address", "phoneInvalid": "Please enter a valid phone number", "urlInvalid": "Please enter a valid URL", "numberInvalid": "Please enter a valid number", "dateInvalid": "Please enter a valid date", "timeInvalid": "Please enter a valid time", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "minValue": "Must be at least {{min}}", "maxValue": "Must be no more than {{max}}", "passwordTooWeak": "Password is too weak", "passwordsNoMatch": "Passwords do not match", "fileTooBig": "File size is too large", "fileTypeInvalid": "Invalid file type", "futureDate": "Date must be in the future", "pastDate": "Date must be in the past", "businessHours": "Time must be within business hours", "duplicateEntry": "This entry already exists", "invalidSelection": "Invalid selection", "invalidFormat": "Invalid format", "businessNameMin": "Business name must be at least 2 characters", "phoneMin": "Please enter a valid phone number", "categoryRequired": "Please select a category", "atLeastOneLocation": "Please add at least one location before continuing.", "confirmDeleteLocation": "Are you sure you want to delete this location?", "atLeastOneService": "Please add at least one service before continuing.", "confirmDeleteService": "Are you sure you want to delete this service?", "failedToLoadServices": "Failed to load services", "confirmDeleteQueue": "Are you sure you want to delete \"{title}\"? This action cannot be undone.", "failedToLoadQueues": "Failed to load queues"}, "hints": {"passwordRequirements": "Password must contain at least 8 characters, including uppercase, lowercase, and number", "phoneFormat": "Enter phone number with country code", "emailFormat": "Enter a valid email address", "urlFormat": "Enter full URL including http:// or https://", "dateFormat": "Use MM/DD/YYYY format", "timeFormat": "Use 24-hour format (HH:MM)", "priceFormat": "Enter amount in local currency", "durationFormat": "Enter duration in minutes", "optionalField": "This field is optional", "requiredField": "This field is required", "autoSave": "Changes are saved automatically", "unsavedChanges": "You have unsaved changes"}, "buttons": {"save": "Save", "saveAndContinue": "Save & Continue", "cancel": "Cancel", "reset": "Reset", "clear": "Clear", "submit": "Submit", "update": "Update", "delete": "Delete", "add": "Add", "remove": "Remove", "edit": "Edit", "duplicate": "Duplicate", "preview": "Preview", "publish": "Publish", "draft": "Save as Draft", "schedule": "Schedule", "reschedule": "Reschedule", "confirm": "Confirm", "reject": "Reject", "approve": "Approve", "archive": "Archive", "restore": "Rest<PERSON>", "upload": "Upload", "uploading": "Uploading...", "removing": "Removing...", "saving": "Saving...", "uploadPicture": "Upload Picture", "uploadLogo": "Upload Logo", "removePicture": "Remove Picture", "removeLogo": "Remove Logo", "completeSetup": "Complete Setup", "skipForNow": "Skip for Now"}}