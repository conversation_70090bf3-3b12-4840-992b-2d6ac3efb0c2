import React, { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { ErrorDisplay } from "../../components/error";
import ConfirmationDialog from "../../components/ui/confirmation/ConfirmationDialog";
import { useConfirmation } from "../../hooks/useConfirmation";
import {
  useQueues,
  useQueueStats,
  useDeleteQueue,
  useQueueLimits,
  useCanCreateQueue,
  queueKeys
} from "../../hooks/useQueues";
import QueueCard from "../../components/queues/QueueCard";
import QueueForm from "../../components/queues/QueueForm";
import QueueDetails from "../../components/queues/QueueDetails";
import QueueStatsOverview from "../../components/queues/QueueStatsOverview";
import QueueLimitsCard from "../../components/queues/QueueLimitsCard";
import {
  CreateQueueButton,
  UsageWarningBanner
} from "../../components/subscription";
import { Queue, QueueFilters } from "../../types/queue";
import { useAuth } from '../../context/AuthContext';
import { useManagementTranslation, useCommonTranslation } from "../../hooks/useTranslation";

// Separate component for queue results to prevent full page re-render
interface QueueResultsProps {
  filters: QueueFilters;
  onViewQueue: (queue: Queue) => void;
  onEditQueue: (queue: Queue) => void;
  onDeleteQueue: (id: number) => void;
  onCreateQueue: () => void;
}

const QueueResults: React.FC<QueueResultsProps> = React.memo(({
  filters,
  onViewQueue,
  onEditQueue,
  onDeleteQueue,
  onCreateQueue
}) => {
  const { data: queues, isLoading, error } = useQueues(filters);
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for queues page
  const queueTranslations = {
    ar: {
      failedToLoad: "فشل في تحميل قوائم الانتظار",
      noQueuesFound: "لم يتم العثور على قوائم انتظار",
      noQueuesFiltered: "لا توجد قوائم انتظار تطابق المرشحات الحالية. حاول تعديل معايير البحث.",
      getStarted: "ابدأ بإنشاء قائمة انتظارك الأولى لإدارة أوقات انتظار العملاء.",
      createFirstQueue: "إنشاء قائمة انتظارك الأولى"
    },
    en: {
      failedToLoad: "Failed to load queues",
      noQueuesFound: "No queues found",
      noQueuesFiltered: "No queues match your current filters. Try adjusting your search criteria.",
      getStarted: "Get started by creating your first queue to manage customer wait times.",
      createFirstQueue: "Create Your First Queue"
    },
    fr: {
      failedToLoad: "Échec du chargement des files d'attente",
      noQueuesFound: "Aucune file d'attente trouvée",
      noQueuesFiltered: "Aucune file d'attente ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.",
      getStarted: "Commencez par créer votre première file d'attente pour gérer les temps d'attente des clients.",
      createFirstQueue: "Créer votre première file d'attente"
    }
  };

  const currentLang = currentLanguage as keyof typeof queueTranslations;
  const qt = (key: keyof typeof queueTranslations.ar) =>
    queueTranslations[currentLang]?.[key] || queueTranslations.en[key] || key;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title={qt('failedToLoad')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      {/* Queues List */}
      {queues && queues.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {queues.map((queue) => (
            <QueueCard
              key={queue.id}
              queue={queue}
              onView={() => onViewQueue(queue)}
              onEdit={() => onEditQueue(queue)}
              onDelete={() => onDeleteQueue(queue.id)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {qt('noQueuesFound')}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {Object.keys(filters).length > 0
              ? qt('noQueuesFiltered')
              : qt('getStarted')
            }
          </p>
          {Object.keys(filters).length === 0 && (
            <Button onClick={onCreateQueue}>
              {qt('createFirstQueue')}
            </Button>
          )}
        </div>
      )}
    </>
  );
});

export default function QueueManagement() {
  const [showQueueForm, setShowQueueForm] = useState(false);
  const [editingQueue, setEditingQueue] = useState<Queue | null>(null);
  const [selectedQueue, setSelectedQueue] = useState<Queue | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [filters, setFilters] = useState<QueueFilters>({});

  const queryClient = useQueryClient();
  const confirmation = useConfirmation();
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();

  // Custom translations for filter labels
  const filterTranslations = {
    ar: {
      status: "الحالة",
      location: "الموقع",
      service: "الخدمة",
      search: "البحث",
      allQueues: "جميع قوائم الانتظار",
      active: "نشط",
      inactive: "غير نشط",
      allLocations: "جميع المواقع",
      allServices: "جميع الخدمات",
      searchPlaceholder: "البحث في قوائم الانتظار..."
    },
    en: {
      status: "Status",
      location: "Location",
      service: "Service",
      search: "Search",
      allQueues: "All Queues",
      active: "Active",
      inactive: "Inactive",
      allLocations: "All Locations",
      allServices: "All Services",
      searchPlaceholder: "Search queues..."
    },
    fr: {
      status: "Statut",
      location: "Emplacement",
      service: "Service",
      search: "Rechercher",
      allQueues: "Toutes les files d'attente",
      active: "Actif",
      inactive: "Inactif",
      allLocations: "Tous les emplacements",
      allServices: "Tous les services",
      searchPlaceholder: "Rechercher des files d'attente..."
    }
  };

  const currentLang = currentLanguage as keyof typeof filterTranslations;
  const ft = (key: keyof typeof filterTranslations.ar) =>
    filterTranslations[currentLang]?.[key] || filterTranslations.en[key] || key;
  // Remove the useQueues hook from here - it's now in QueueResults component
  const { data: queueStats } = useQueueStats();
  const { data: queueLimits } = useQueueLimits();
  const { data: canCreateQueue } = useCanCreateQueue();
  const deleteQueueMutation = useDeleteQueue();
  const { fetchSubscriptionData , fetchUsedCreditsData  } = useAuth()

  React.useEffect(() => {
    console.log("Queue Page reload")
    fetchSubscriptionData();
    fetchUsedCreditsData();
  }, []);

  // Debug state changes (moved after variable declarations)
  React.useEffect(() => {
    console.log('🔄 showQueueForm state changed:', showQueueForm);
  }, [showQueueForm]);

  React.useEffect(() => {
    console.log('📊 canCreateQueue data loaded:', canCreateQueue);
  }, [canCreateQueue]);

  const handleCreateQueue = () => {
    console.log('🎯 Create Queue button clicked');
    console.log('📊 canCreateQueue data:', canCreateQueue);

    // Test with simple alert first
    // alert('Create Queue button clicked! Check console for details.');

    // Temporarily disable limit check for debugging
    // if (canCreateQueue && !canCreateQueue.canCreate) {
    //   console.log('❌ Cannot create queue - limit reached');
    //   alert(canCreateQueue.message || 'Queue limit reached. Please upgrade your subscription.');
    //   return;
    // }

    console.log('✅ Opening queue form modal');
    setEditingQueue(null);
    setShowQueueForm(true);
  };

  const handleEditQueue = (queue: Queue) => {
    setEditingQueue(queue);
    setShowQueueForm(true);
  };

  const handleViewQueue = (queue: Queue) => {
    setSelectedQueue(queue);
    setShowDetails(true);
  };

  const handleDeleteQueue = async (id: number) => {
    const confirmed = await confirmation.confirm({
      title: 'Delete Queue',
      message: `Are you sure you want to delete this queue? This action cannot be undone and will remove all associated data.`,
      confirmText: 'Delete Queue',
      cancelText: 'Cancel',
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteQueueMutation.mutateAsync(id);
        // The mutation already handles cache invalidation, but we can also refresh subscription data
        fetchSubscriptionData();
        fetchUsedCreditsData();
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleCloseForm = () => {
    setShowQueueForm(false);
    setEditingQueue(null);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedQueue(null);
  };

  const handleQueueSuccess = () => {
    // Invalidate all queue-related queries to ensure fresh data
    queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
    queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
    queryClient.invalidateQueries({ queryKey: queueKeys.limits() });
    queryClient.invalidateQueries({ queryKey: queueKeys.canCreate() });

    // Also refresh subscription data
    fetchSubscriptionData();
    fetchUsedCreditsData();
    handleCloseForm();
  };

  const handleFilterChange = (newFilters: Partial<QueueFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <>
      <PageMeta
        title="Queue Management | Provider Dashboard"
        description="Manage customer queues, wait times, and queue-based appointments"
      />
      <PageBreadcrumb pageTitle={tCommon('navigation.queues')} />

      <div className="space-y-6">
        {/* Usage Warning Banner */}
        <UsageWarningBanner />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('queues.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('queues.description')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <CreateQueueButton
              onCreateQueue={handleCreateQueue}
              size="sm"
            />
          </div>
        </div>

        {/* Queue Statistics */}
        <div className="space-y-6">
          {queueStats && <QueueStatsOverview stats={queueStats} />}
        </div>

        {/* Queue Limits */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            {queueLimits ? (
              <QueueLimitsCard limits={queueLimits} />
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            )}
          </div>
          <div>
            {/* Real-time status will be handled by QueueResults component */}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('status')}
              </label>
              <select
                value={filters.isActive === undefined ? '' : filters.isActive ? 'active' : 'inactive'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({
                    isActive: value === '' ? undefined : value === 'active'
                  });
                }}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{ft('allQueues')}</option>
                <option value="active">{ft('active')}</option>
                <option value="inactive">{ft('inactive')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('location')}
              </label>
              <select
                value={filters.locationId || ''}
                onChange={(e) => handleFilterChange({
                  locationId: e.target.value ? Number(e.target.value) : undefined
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{ft('allLocations')}</option>
                {/* Add location options here */}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('service')}
              </label>
              <select
                value={filters.serviceId || ''}
                onChange={(e) => handleFilterChange({
                  serviceId: e.target.value ? Number(e.target.value) : undefined
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{ft('allServices')}</option>
                {/* Add service options here */}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('search')}
              </label>
              <input
                type="text"
                placeholder={ft('searchPlaceholder')}
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Queues List - Now handled by QueueResults component */}
        <QueueResults
          filters={filters}
          onViewQueue={handleViewQueue}
          onEditQueue={handleEditQueue}
          onDeleteQueue={handleDeleteQueue}
          onCreateQueue={handleCreateQueue}
        />
      </div>

      {/* Queue Form Modal */}
      <Modal
        isOpen={showQueueForm}
        onClose={handleCloseForm}
        className="max-w-2xl p-0"
      >
        <QueueForm
          queue={editingQueue}
          onClose={handleCloseForm}
          onSuccess={handleQueueSuccess}
        />
      </Modal>

      {/* Queue Details Modal */}
      <Modal
        isOpen={showDetails && !!selectedQueue}
        onClose={handleCloseDetails}
        className="max-w-4xl p-0"
      >
        {selectedQueue && (
          <QueueDetails
            queue={selectedQueue}
            onClose={handleCloseDetails}
            onEdit={() => {
              setEditingQueue(selectedQueue);
              setShowDetails(false);
              setShowQueueForm(true);
            }}
          />
        )}
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </>
  );
}
