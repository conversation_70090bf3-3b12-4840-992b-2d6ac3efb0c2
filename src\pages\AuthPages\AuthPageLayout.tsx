import React from "react";
import GridShape from "../../components/common/GridShape";
import { Link } from "react-router";
import ThemeTogglerTwo from "../../components/common/ThemeTogglerTwo";
import { CompactLanguageSwitcher } from "../../components/common/LanguageSwitcher";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative p-6 bg-white z-1 dark:bg-gray-900 sm:p-0">
      <div className="relative flex flex-col justify-center w-full h-screen lg:flex-row dark:bg-gray-900 sm:p-0">
        {children}
        <div className="items-center hidden w-full h-full lg:w-1/2 bg-brand-950 dark:bg-white/5 lg:grid">
          <div className="relative flex items-center justify-center z-1">
            {/* <!-- ===== Common Grid Shape Start ===== --> */}
            <GridShape />
            <div className="flex flex-col items-center max-w-xs">
              <Link to="/" className="block mb-4">
                <img
                  width={150}
                  height={48}
                  src="/images/logo/logo-dark2.png"
                  alt="Dalti Provider Dashboard"
                />
              </Link>
              {/* <p className="text-center text-gray-400 dark:text-white/60">
                Dalti Provider Dashboard
              </p> */}
            </div>
          </div>
        </div>
        {/* Language Switcher - Top Right */}
        <div className="fixed z-50 top-4 right-4 sm:top-6 sm:right-6">
          <CompactLanguageSwitcher />
        </div>

        {/* Theme Toggler - Bottom Right */}
        <div className="fixed z-50 hidden bottom-6 right-6 sm:block">
          <ThemeTogglerTwo />
        </div>
      </div>
    </div>
  );
}
