import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { Modal } from '../ui/modal';
import { ProviderCustomer } from '../../types/provider-customer';
import { DownloadIcon, XMarkIcon, CheckCircleIcon } from '../../icons';
import { useManagementTranslation, useCommonTranslation } from '../../hooks/useTranslation';

interface CustomerExportProps {
  customers: ProviderCustomer[];
  isOpen: boolean;
  onClose: () => void;
}

type ExportFormat = 'csv' | 'json' | 'excel';
type ExportFields = {
  firstName: boolean;
  lastName: boolean;
  mobileNumber: boolean;
  email: boolean;
  nationalId: boolean;
  notes: boolean;
  appointmentCount: boolean;
  createdAt: boolean;
  isActive: boolean;
};

export default function CustomerExport({ customers, isOpen, onClose }: CustomerExportProps) {
  // Ensure customers is always an array for safe operations
  const safeCustomers = Array.isArray(customers) ? customers : [];
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();

  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [exportFields, setExportFields] = useState<ExportFields>({
    firstName: true,
    lastName: true,
    mobileNumber: true,
    email: true,
    nationalId: false,
    notes: false,
    appointmentCount: true,
    createdAt: true,
    isActive: true,
  });
  const [includeInactive, setIncludeInactive] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const handleFieldToggle = (field: keyof ExportFields) => {
    setExportFields(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const selectAllFields = () => {
    const allSelected = Object.values(exportFields).every(Boolean);
    const newState = !allSelected;
    setExportFields({
      firstName: newState,
      lastName: newState,
      mobileNumber: newState,
      email: newState,
      nationalId: newState,
      notes: newState,
      appointmentCount: newState,
      createdAt: newState,
      isActive: newState,
    });
  };

  const getFilteredCustomers = () => {
    return includeInactive ? safeCustomers : safeCustomers.filter(c => c.isActive !== false);
  };

  const getSelectedFields = () => {
    return Object.entries(exportFields)
      .filter(([_, selected]) => selected)
      .map(([field, _]) => field);
  };

  const getFieldLabel = (field: string) => {
    const fieldLabels: Record<string, string> = {
      firstName: t('customers.export.fieldLabels.firstName', 'First Name'),
      lastName: t('customers.export.fieldLabels.lastName', 'Last Name'),
      mobileNumber: t('customers.export.fieldLabels.mobileNumber', 'Mobile Number'),
      email: t('customers.export.fieldLabels.email', 'Email'),
      nationalId: t('customers.export.fieldLabels.nationalId', 'National ID'),
      notes: t('customers.export.fieldLabels.notes', 'Notes'),
      appointmentCount: t('customers.export.fieldLabels.appointmentCount', 'Appointment Count'),
      createdAt: t('customers.export.fieldLabels.createdAt', 'Created At'),
      isActive: t('customers.export.fieldLabels.isActive', 'Is Active'),
    };
    return fieldLabels[field] || field;
  };

  const formatCustomerData = (customer: ProviderCustomer) => {
    const data: any = {};

    if (exportFields.firstName) data[t('customers.export.fields.firstName', 'First Name')] = customer.firstName;
    if (exportFields.lastName) data[t('customers.export.fields.lastName', 'Last Name')] = customer.lastName;
    if (exportFields.mobileNumber) data[t('customers.export.fields.mobileNumber', 'Mobile Number')] = customer.mobileNumber;
    if (exportFields.email) data[t('customers.export.fields.email', 'Email')] = customer.email || '';
    if (exportFields.nationalId) data[t('customers.export.fields.nationalId', 'National ID')] = customer.nationalId || '';
    if (exportFields.notes) data[t('customers.export.fields.notes', 'Notes')] = customer.notes || '';
    if (exportFields.appointmentCount) data[t('customers.export.fields.appointmentCount', 'Appointment Count')] = customer.appointmentCount;
    if (exportFields.createdAt) data[t('customers.export.fields.createdAt', 'Created At')] = new Date(customer.createdAt).toLocaleDateString();
    if (exportFields.isActive) data[t('customers.export.fields.status', 'Status')] = customer.isActive !== false ? tCommon('status.active', 'Active') : tCommon('status.inactive', 'Inactive');

    return data;
  };

  const exportToCSV = () => {
    const filteredCustomers = getFilteredCustomers();
    const formattedData = filteredCustomers.map(formatCustomerData);
    
    if (formattedData.length === 0) {
      alert(t('customers.export.noCustomersToExport', 'No customers to export'));
      return;
    }

    const headers = Object.keys(formattedData[0]);
    const csvContent = [
      headers.join(','),
      ...formattedData.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    downloadFile(csvContent, 'customers.csv', 'text/csv');
  };

  const exportToJSON = () => {
    const filteredCustomers = getFilteredCustomers();
    const formattedData = filteredCustomers.map(formatCustomerData);
    
    const jsonContent = JSON.stringify(formattedData, null, 2);
    downloadFile(jsonContent, 'customers.json', 'application/json');
  };

  const exportToExcel = () => {
    // For Excel export, we'll use CSV format with .xlsx extension
    // In a real application, you might want to use a library like xlsx
    const filteredCustomers = getFilteredCustomers();
    const formattedData = filteredCustomers.map(formatCustomerData);
    
    if (formattedData.length === 0) {
      alert(t('customers.export.noCustomersToExport', 'No customers to export'));
      return;
    }

    const headers = Object.keys(formattedData[0]);
    const csvContent = [
      headers.join('\t'), // Use tabs for better Excel compatibility
      ...formattedData.map(row => 
        headers.map(header => row[header]).join('\t')
      )
    ].join('\n');

    downloadFile(csvContent, 'customers.xlsx', 'application/vnd.ms-excel');
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate export processing time
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      switch (exportFormat) {
        case 'csv':
          exportToCSV();
          break;
        case 'json':
          exportToJSON();
          break;
        case 'excel':
          exportToExcel();
          break;
      }
      
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert(t('customers.export.exportFailed', 'Export failed. Please try again.'));
    } finally {
      setIsExporting(false);
    }
  };

  const filteredCustomers = getFilteredCustomers();
  const selectedFieldsCount = getSelectedFields().length;

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-2xl p-0">
      <div className="bg-white dark:bg-gray-800 rounded-3xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {t('customers.export.title', 'Export Customers')}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {t('customers.export.description', 'Export your customer data in various formats')}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Export Format */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t('customers.export.exportFormat', 'Export Format')}
            </label>
            <div className="grid grid-cols-3 gap-3">
              {[
                { value: 'csv', label: 'CSV', description: t('customers.export.formats.csvDescription', 'Comma-separated values') },
                { value: 'json', label: 'JSON', description: t('customers.export.formats.jsonDescription', 'JavaScript Object Notation') },
                { value: 'excel', label: 'Excel', description: t('customers.export.formats.excelDescription', 'Microsoft Excel format') },
              ].map((format) => (
                <button
                  key={format.value}
                  onClick={() => setExportFormat(format.value as ExportFormat)}
                  className={`p-3 rounded-lg border text-left transition-colors ${
                    exportFormat === format.value
                      ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {format.label}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {format.description}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Fields Selection */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('customers.export.fieldsToExport', 'Fields to Export ({{count}} selected)', { count: selectedFieldsCount })}
              </label>
              <button
                onClick={selectAllFields}
                className="text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
              >
                {Object.values(exportFields).every(Boolean) ? t('customers.export.deselectAll', 'Deselect All') : t('customers.export.selectAll', 'Select All')}
              </button>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(exportFields).map(([field, selected]) => (
                <label
                  key={field}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selected}
                    onChange={() => handleFieldToggle(field as keyof ExportFields)}
                    className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {getFieldLabel(field)}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Options */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t('customers.export.exportOptions', 'Export Options')}
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={includeInactive}
                onChange={(e) => setIncludeInactive(e.target.checked)}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('customers.export.includeInactiveCustomers', 'Include inactive customers')}
              </span>
            </label>
          </div>

          {/* Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <CheckCircleIcon className="w-4 h-4 text-green-500" />
              <span>
                {t('customers.export.readyToExport', 'Ready to export {{customerCount}} customers with {{fieldCount}} fields', {
                  customerCount: filteredCustomers.length,
                  fieldCount: selectedFieldsCount
                })}
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isExporting}
          >
            {tCommon('common.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || selectedFieldsCount === 0}
            loading={isExporting}
          >
            <DownloadIcon className="w-4 h-4 mr-2" />
            {t('customers.export.exportButton', 'Export {{format}}', { format: exportFormat.toUpperCase() })}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
