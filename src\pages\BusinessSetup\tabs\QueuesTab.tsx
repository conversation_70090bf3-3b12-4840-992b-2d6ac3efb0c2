import React, { useState } from 'react';
import Button from "../../../components/ui/button/Button";
import { Modal } from "../../../components/ui/modal";
import { ErrorDisplay } from "../../../components/error";
import { useConfirmation } from "../../../hooks/useConfirmation";
import ConfirmationDialog from "../../../components/ui/confirmation/ConfirmationDialog";
import { useQueues, useDeleteQueue } from "../../../hooks/useQueues";
import QueueForm from "../../../components/queues/QueueForm";
import QueueCard from "../../../components/queues/QueueCard";
import QueueDetailsModal from "../../../components/queues/QueueDetailsModal";

import {
  CreateQueueButton,
  UsageWarningBanner
} from "../../../components/subscription";
import { Queue, QueueFilters } from "../../../types/queue";
import { useManagementTranslation, useCommonTranslation } from "../../../hooks/useTranslation";
import { useRTL } from "../../../context/LanguageContext";

// Separate component for queue results
interface QueueResultsProps {
  filters: QueueFilters;
  onViewQueue: (queue: Queue) => void;
  onEditQueue: (queue: Queue) => void;
  onDeleteQueue: (id: number) => void;
  onCreateQueue: () => void;
  deleteQueueMutation: any;
}

const QueueResults: React.FC<QueueResultsProps> = React.memo(({
  filters,
  onViewQueue,
  onEditQueue,
  onDeleteQueue,
  onCreateQueue,
  deleteQueueMutation
}) => {
  const { data: queues, isLoading, error } = useQueues(filters);
  const { t } = useManagementTranslation();
  const { isRTL, direction } = useRTL();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12" dir={direction}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600"></div>
        <p className={`text-gray-600 dark:text-gray-400 ${isRTL ? 'mr-3' : 'ml-3'}`}>
          {t('queues.loadingQueues', 'Loading queues...')}
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div dir={direction}>
        <ErrorDisplay
          error={error}
          title={t('queues.failedToLoad', 'Failed to load queues')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!queues || queues.length === 0) {
    return (
      <div className="text-center py-12" dir={direction}>
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('queues.noQueuesFound', 'No queues found')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {t('queues.getStarted', 'Get started by creating your first appointment queue.')}
        </p>
        <CreateQueueButton onCreateQueue={onCreateQueue} />
      </div>
    );
  }

  return (
    <div className="space-y-6" dir={direction}>
      <div className={`flex items-center  justify-between`}>
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('queues.yourQueues', 'Your Queues')} ({queues.length})
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('queues.manageQueues', 'Manage your appointment queues and waiting systems')}
          </p>
        </div>
        <CreateQueueButton onCreateQueue={onCreateQueue} />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {queues.map((queue) => (
          <QueueCard
            key={queue.id}
            queue={queue}
            onView={() => onViewQueue(queue)}
            onEdit={() => onEditQueue(queue)}
            onDelete={() => onDeleteQueue(queue.id)}
          />
        ))}
      </div>
    </div>
  );
});

QueueResults.displayName = 'QueueResults';

export default function QueuesTab() {
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { isRTL, direction } = useRTL();
  const [filters, setFilters] = useState<QueueFilters>({});
  const [editingQueue, setEditingQueue] = useState<Queue | null>(null);
  const [showQueueForm, setShowQueueForm] = useState(false);
  const [selectedQueue, setSelectedQueue] = useState<Queue | null>(null);
  const [showQueueDetails, setShowQueueDetails] = useState(false);

  const deleteQueueMutation = useDeleteQueue();
  const confirmation = useConfirmation();

  const handleCreateQueue = () => {
    setEditingQueue(null);
    setShowQueueForm(true);
  };

  const handleViewQueue = (queue: Queue) => {
    setSelectedQueue(queue);
    setShowQueueDetails(true);
  };

  const handleEditQueue = (queue: Queue) => {
    setEditingQueue(queue);
    setShowQueueForm(true);
  };

  const handleEditFromDetails = () => {
    if (selectedQueue) {
      setShowQueueDetails(false);
      setEditingQueue(selectedQueue);
      setShowQueueForm(true);
    }
  };

  const handleCloseDetails = () => {
    setShowQueueDetails(false);
    setSelectedQueue(null);
  };

  const handleDeleteQueue = async (id: number) => {
    const confirmed = await confirmation.confirm({
      title: t('queues.deleteConfirmTitle', 'Delete Queue'),
      message: t('queues.deleteConfirmMessage', 'Are you sure you want to delete this queue? This action cannot be undone and will remove all associated data.'),
      confirmText: tCommon('actions.delete'),
      cancelText: tCommon('actions.cancel'),
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteQueueMutation.mutateAsync(id);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleQueueFormSuccess = () => {
    setShowQueueForm(false);
    setEditingQueue(null);
  };

  const handleCloseForm = () => {
    setShowQueueForm(false);
    setEditingQueue(null);
  };

  return (
    <div className="space-y-6" dir={direction}>
      {/* Usage Warning Banner */}
      <UsageWarningBanner />

      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t('queues.title', 'Queues Management')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          {t('queues.subtitle', 'Configure appointment queues and waiting systems')}
        </p>
      </div>

      {/* Queue Stats and Limits */}
      {/* <div className="grid gap-6 lg:grid-cols-2">
        {queueStats && <QueueStatsOverview stats={queueStats} />}
        {queueLimits && <QueueLimitsCard limits={queueLimits} />}
      </div> */}

      {/* Queues List */}
      <QueueResults
        filters={filters}
        onViewQueue={handleViewQueue}
        onEditQueue={handleEditQueue}
        onDeleteQueue={handleDeleteQueue}
        onCreateQueue={handleCreateQueue}
        deleteQueueMutation={deleteQueueMutation}
      />

      {/* Queue Form Modal */}
      <Modal
        isOpen={showQueueForm}
        onClose={handleCloseForm}
        className="max-w-2xl"
        showCloseButton={false}
      >
        <QueueForm
          queue={editingQueue}
          onClose={handleCloseForm}
          onSuccess={handleQueueFormSuccess}
        />
      </Modal>

      {/* Queue Details Modal */}
      <Modal
        isOpen={showQueueDetails && !!selectedQueue}
        onClose={handleCloseDetails}
        className="max-w-4xl"
        showCloseButton={false}
      >
        {selectedQueue && (
          <QueueDetailsModal
            queue={selectedQueue}
            onClose={handleCloseDetails}
            onEdit={handleEditFromDetails}
          />
        )}
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
