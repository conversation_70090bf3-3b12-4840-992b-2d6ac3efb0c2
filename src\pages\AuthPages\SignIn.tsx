import PageMeta from "../../components/common/PageMeta";
import AuthLayout from "./AuthPageLayout";
import SignInForm from "../../components/auth/SignInForm";
import { useAuthTranslation } from "../../hooks/useTranslation";

export default function SignIn() {
  const { t } = useAuthTranslation();

  return (
    <>
      <PageMeta
        title={`${t('signIn.title')} | Dalti Provider Dashboard`}
        description="Sign in to your Dalti Provider Dashboard to manage appointments, patients, and practice operations"
      />
      <AuthLayout>
        <SignInForm />
      </AuthLayout>
    </>
  );
}
