import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from '../../context/AuthContext';
import { useProfileCompletion } from '../../hooks/useProfileCompletion';
import PageMeta from '../../components/common/PageMeta';
import Button from '../../components/ui/button/Button';
import { CompletionSection } from '../../types/profile-completion';
import SetupModal from '../../components/setup/SetupModal';
import BusinessInfoSetupModal from '../../components/setup/BusinessInfoSetupModal';
import ServicesSetupModal from '../../components/setup/ServicesSetupModal';
import LocationsSetupModal from '../../components/setup/LocationsSetupModal';
import QueuesSetupModal from '../../components/setup/QueuesSetupModal';
import { useTranslation, useCommonTranslation } from '../../hooks/useTranslation';
import { CompactLanguageSwitcher } from '../../components/common/LanguageSwitcher';

export default function ProviderSetup() {
  const navigate = useNavigate();
  const { provider, updateProvider } = useAuth();
  const { completion, isLoading, error, refetch } = useProfileCompletion();
  const { t } = useTranslation('dashboard');
  const { t: tCommon } = useCommonTranslation();
  const [activeSection, setActiveSection] = useState<CompletionSection | null>(null);

  // Function to translate API response messages
  const translateApiMessage = (message: string): string => {
    if (!message) return '';

    // Common API message patterns and their translations
    const messageMap: { [key: string]: string } = {
      'Upload a professional profile picture': t('setup.apiMessages.uploadProfessionalPicture'),
      'Missing: Business description/presentation': t('setup.apiMessages.missingBusinessDescription'),
      'No Logo uploaded': t('setup.apiMessages.noLogoUploaded'),
    };

    // Check for exact matches first
    if (messageMap[message]) {
      return messageMap[message];
    }

    // Check for pattern matches with numbers
    const locationMatch = message.match(/All (\d+) location\(s\) have complete information/);
    if (locationMatch) {
      return t('setup.apiMessages.allLocationsComplete', { count: locationMatch[1] });
    }

    const serviceMatch = message.match(/(\d+) service\(s\) configured/);
    if (serviceMatch) {
      return t('setup.apiMessages.servicesConfigured', { count: serviceMatch[1] });
    }

    const queueMatch = message.match(/(\d+) active queue\(s\) available for booking/);
    if (queueMatch) {
      return t('setup.apiMessages.activeQueuesAvailable', { count: queueMatch[1] });
    }

    // Add spacing around any checkmark or warning icons in the message
    let processedMessage = message
      .replace(/✅/g, '✅ ')  // Add space after checkmark
      .replace(/⚠️/g, '⚠️ ')  // Add space after warning
      .replace(/❌/g, '❌ ')  // Add space after X mark
      .replace(/🎉/g, '🎉 ')  // Add space after celebration
      .replace(/  /g, ' ');   // Remove double spaces

    // Return processed message if no translation found
    return processedMessage;
  };
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Handle completing setup (mark as complete in provider profile)
  const handleCompleteSetup = () => {
    if (provider) {
      updateProvider({
        ...provider,
        isSetupComplete: true,
      });
    }
    navigate('/', { replace: true });
  };

  // Open modal for a specific section
  const openSectionModal = (section: CompletionSection) => {
    console.log('🔧 Opening setup modal for section:', section);
    setActiveSection(section);
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setActiveSection(null);
  };

  // Handle modal completion
  const handleModalComplete = () => {
    closeModal();
    // Refresh completion data
    refetch();
  };

  // Determine setup steps based on profile completion data
  const setupSteps = completion ? [
    {
      id: 'providerInfo' as CompletionSection,
      title: t('setup.businessInformation'),
      description: t('setup.businessInformationDesc'),
      completed: completion.breakdown.providerInfo.completed,
      percentage: completion.breakdown.providerInfo.percentage,
      details: completion.breakdown.providerInfo.details,
    },
    {
      id: 'profilePicture' as CompletionSection,
      title: t('setup.businessLogo'),
      description: t('setup.businessLogoDesc'),
      completed: completion.breakdown.profilePicture.completed,
      percentage: completion.breakdown.profilePicture.percentage,
      details: completion.breakdown.profilePicture.details,
    },
    {
      id: 'providingPlaces' as CompletionSection,
      title: t('setup.locationHours'),
      description: t('setup.locationHoursDesc'),
      completed: completion.breakdown.providingPlaces.completed,
      percentage: completion.breakdown.providingPlaces.percentage,
      details: completion.breakdown.providingPlaces.details,
    },
    {
      id: 'services' as CompletionSection,
      title: t('setup.servicesPricing'),
      description: t('setup.servicesPricingDesc'),
      completed: completion.breakdown.services.completed,
      percentage: completion.breakdown.services.percentage,
      details: completion.breakdown.services.details,
    },
    {
      id: 'queues' as CompletionSection,
      title: t('setup.appointmentQueues'),
      description: t('setup.appointmentQueuesDesc'),
      completed: completion.breakdown.queues.completed,
      percentage: completion.breakdown.queues.percentage,
      details: completion.breakdown.queues.details,
    },
  ] : [
    {
      id: 'providerInfo' as CompletionSection,
      title: t('setup.businessInformation'),
      description: t('setup.businessInformationDesc'),
      completed: !!provider?.title,
      percentage: !!provider?.title ? 100 : 0,
      details: '',
    },
    {
      id: 'profilePicture' as CompletionSection,
      title: t('setup.businessLogo'),
      description: t('setup.businessLogoDesc'),
      completed: !!provider?.logo,
      percentage: !!provider?.logo ? 100 : 0,
      details: '',
    },
    {
      id: 'providingPlaces' as CompletionSection,
      title: t('setup.locationHours'),
      description: t('setup.locationHoursDesc'),
      completed: false,
      percentage: 0,
      details: '',
    },
    {
      id: 'services' as CompletionSection,
      title: t('setup.servicesPricing'),
      description: t('setup.servicesPricingDesc'),
      completed: false,
      percentage: 0,
      details: '',
    },
    {
      id: 'queues' as CompletionSection,
      title: t('setup.appointmentQueues'),
      description: t('setup.appointmentQueuesDesc'),
      completed: false,
      percentage: 0,
      details: '',
    },
  ];

  const completedSteps = setupSteps.filter(step => step.completed).length;
  const progressPercentage = completion ? completion.overallPercentage : (completedSteps / setupSteps.length) * 100;

  // Check if setup is functionally complete (80%+)
  const isFunctionallyComplete = progressPercentage >= 80;
  const isPerfectSetup = progressPercentage >= 100;

  // Get next recommended step
  const nextStep = setupSteps.find(step => !step.completed);
  const nextStepFromAPI = completion?.nextSteps[0];

  // Refresh completion data when component mounts
  useEffect(() => {
    if (!isLoading && !completion) {
      refetch();
    }
  }, [isLoading, completion, refetch]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">{tCommon('messages.loadingSetupProgress')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Complete Your Setup | Provider Dashboard"
        description="Complete your provider setup to start accepting appointments"
      />

      {/* Language Switcher - Fixed Top Right */}
      <div className="fixed z-50 top-4 right-4 sm:top-6 sm:right-6">
        <CompactLanguageSwitcher />
      </div>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="px-6 py-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {isPerfectSetup ? t('setup.setupComplete') : t('setup.welcomeTitle')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isPerfectSetup
                    ? t('setup.setupCompleteDesc')
                    : isFunctionallyComplete
                    ? t('setup.readyToAcceptDesc')
                    : t('setup.welcomeDesc')
                  }
                </p>
              </div>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('setup.setupProgress')}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {t('setup.percentComplete', { percent: Math.round(progressPercentage) })}
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${
                      isPerfectSetup ? 'bg-green-500' :
                      isFunctionallyComplete ? 'bg-blue-500' :
                      progressPercentage >= 50 ? 'bg-yellow-500' :
                      progressPercentage >= 25 ? 'bg-orange-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <span>{t('setup.gettingStarted')}</span>
                  <span>{t('setup.functional')}</span>
                  <span>{t('setup.perfectSetup')}</span>
                </div>
              </div>

              {/* Status Message */}
              {isPerfectSetup ? (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                  <div className="flex items-center">
                    <span className="text-green-500 me-3 text-xl">🎉</span>
                    <div>
                      <h3 className="text-green-800 dark:text-green-200 font-medium">Perfect Setup Complete!</h3>
                      <p className="text-green-700 dark:text-green-300 text-sm">
                        Your account is fully optimized and ready to provide the best experience for your customers.
                      </p>
                    </div>
                  </div>
                </div>
              ) : isFunctionallyComplete ? (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                  <div className="flex items-center">
                    <span className="text-blue-500 me-3 text-xl">✅</span>
                    <div>
                      <h3 className="text-blue-800 dark:text-blue-200 font-medium">{t('setup.readyToAccept')}</h3>
                      <p className="text-blue-700 dark:text-blue-300 text-sm">
                        {t('setup.minimumRequirements')}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-900/20 dark:border-amber-800">
                  <div className="flex items-center">
                    <span className="text-amber-500 me-3 text-xl">⚠️</span>
                    <div>
                      <h3 className="text-amber-800 dark:text-amber-200 font-medium">{t('setup.setupRequired')}</h3>
                      <p className="text-amber-700 dark:text-amber-300 text-sm">
                        {t('setup.setupRequiredDesc')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Next Step Recommendation */}
              {nextStepFromAPI && !isPerfectSetup && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                  <h3 className="text-blue-800 dark:text-blue-200 font-medium mb-2">{t('setup.recommendedNextStep')}:</h3>
                  <p className="text-blue-700 dark:text-blue-300 text-sm">{translateApiMessage(nextStepFromAPI)}</p>
                </div>
              )}

              {/* Setup Steps */}
              <div className="space-y-4 mb-8">
                {setupSteps.map((step, index) => (
                  <div
                    key={index}
                    className={`flex items-start p-4 rounded-lg border ${
                      step.completed
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                        : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    <div className="flex-shrink-0 me-5">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          step.completed
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {step.completed ? (
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        ) : (
                          <span className="text-sm font-medium">{index + 1}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3
                        className={`text-sm font-medium ${
                          step.completed
                            ? 'text-green-800 dark:text-green-200'
                            : 'text-gray-900 dark:text-white'
                        }`}
                      >
                        {step.title}
                      </h3>
                      <p
                        className={`text-sm ${
                          step.completed
                            ? 'text-green-600 dark:text-green-300'
                            : 'text-gray-500 dark:text-gray-400'
                        }`}
                      >
                        {step.details ? translateApiMessage(step.details) : step.description}
                      </p>

                      {/* Step Progress Bar (if partially complete) */}
                      {!step.completed && step.percentage > 0 && (
                        <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                          <div
                            className="bg-yellow-500 h-1.5 rounded-full"
                            style={{ width: `${step.percentage}%` }}
                          />
                        </div>
                      )}
                    </div>

                    {/* Status and Action */}
                    <div className="ms-6 flex flex-col items-end">
                      {/* Status Badge */}
                      {step.completed ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          {t('setup.completed')}
                        </span>
                      ) : step.percentage > 0 ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                          {t('setup.percentComplete', { percent: step.percentage })}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          {t('setup.notStarted')}
                        </span>
                      )}

                      {/* Action Button */}
                      <button
                        onClick={() => openSectionModal(step.id)}
                        className={`mt-3 px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                          step.completed
                            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                            : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600'
                        }`}
                      >
                        {step.completed ? t('setup.edit') : step.percentage > 0 ? t('setup.continue') : t('setup.start')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                {isPerfectSetup ? (
                  <Button
                    onClick={handleCompleteSetup}
                    className="flex-1 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600"
                  >
                    {t('setup.goToDashboard')}
                  </Button>
                ) : isFunctionallyComplete ? (
                  <>
                    <Button
                      onClick={() => openSectionModal(nextStep?.id || 'providerInfo')}
                      variant="outline"
                      className="flex-1"
                    >
                      {t('setup.completeRemainingSteps')}
                    </Button>
                    <Button
                      onClick={handleCompleteSetup}
                      className="flex-1"
                    >
                      {t('setup.goToDashboard')}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={() => openSectionModal(nextStep?.id || 'providerInfo')}
                      variant="outline"
                      className="flex-1"
                    >
                      {t('setup.continueSetup')}
                    </Button>
                    <Button
                      onClick={handleCompleteSetup}
                      className="flex-1"
                    >
                      {t('setup.skipForNow')}
                    </Button>
                  </>
                )}
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {isPerfectSetup
                    ? t('setup.accountReadyToGo')
                    : isFunctionallyComplete
                    ? t('setup.functionalButBetter')
                    : t('setup.completeLater')
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Setup Modals */}
      {activeSection === 'providerInfo' || activeSection === 'profilePicture' ? (
        <SetupModal
          isOpen={isModalOpen}
          onClose={closeModal}
          section={activeSection}
          title={t('setup.businessInfoAndLogo')}
        >
          <BusinessInfoSetupModal
            onComplete={handleModalComplete}
            onCancel={closeModal}
          />
        </SetupModal>
      ) : activeSection === 'services' ? (
        <SetupModal
          isOpen={isModalOpen}
          onClose={closeModal}
          section={activeSection}
          title={t('setup.servicesPricing')}
        >
          <ServicesSetupModal
            onComplete={handleModalComplete}
            onCancel={closeModal}
          />
        </SetupModal>
      ) : activeSection === 'providingPlaces' ? (
        <SetupModal
          isOpen={isModalOpen}
          onClose={closeModal}
          section={activeSection}
          title={t('setup.businessLocations')}
        >
          <LocationsSetupModal
            onComplete={handleModalComplete}
            onCancel={closeModal}
          />
        </SetupModal>
      ) : activeSection === 'queues' ? (
        <SetupModal
          isOpen={isModalOpen}
          onClose={closeModal}
          section={activeSection}
          title={t('setup.appointmentQueues')}
        >
          <QueuesSetupModal
            onComplete={handleModalComplete}
            onCancel={closeModal}
          />
        </SetupModal>
      ) : null}
    </>
  );
}
