import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import ConfirmationDialog from "../../components/ui/confirmation/ConfirmationDialog";
import { useConfirmation } from "../../hooks/useConfirmation";
import { useServices, useServiceCategories, useDeleteService } from "../../hooks/useServices";
import ServiceForm from "../../components/services/ServiceForm";
import ServiceCard from "../../components/services/ServiceCard";
import ServiceCategoryManager from "../../components/services/ServiceCategoryManager";
import {
  CreateServiceButton,
  UsageWarningBanner
} from "../../components/subscription";
import { Service, ServiceFilters } from "../../types";
import { useManagementTranslation, useCommonTranslation } from "../../hooks/useTranslation";

// Separate component for service results to prevent full page re-render
interface ServiceResultsProps {
  filters: ServiceFilters;
  onEditService: (service: Service) => void;
  onDeleteService: (id: number) => void;
  onCreateService: () => void;
  deleteServiceMutation: any;
}

const ServiceResults: React.FC<ServiceResultsProps> = React.memo(({
  filters,
  onEditService,
  onDeleteService,
  onCreateService,
  deleteServiceMutation
}) => {
  const { data: services, isLoading, error } = useServices(filters);
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for services page
  const serviceTranslations = {
    ar: {
      noServicesFound: "لم يتم العثور على خدمات",
      noServicesFiltered: "لا توجد خدمات تطابق المرشحات الحالية. حاول تعديل معايير البحث.",
      getStarted: "ابدأ بإنشاء خدمتك الأولى.",
      createFirstService: "إنشاء خدمتك الأولى",
      failedToLoad: "فشل في تحميل الخدمات"
    },
    en: {
      noServicesFound: "No services found",
      noServicesFiltered: "No services match your current filters. Try adjusting your search criteria.",
      getStarted: "Get started by creating your first service.",
      createFirstService: "Create Your First Service",
      failedToLoad: "Failed to load services"
    },
    fr: {
      noServicesFound: "Aucun service trouvé",
      noServicesFiltered: "Aucun service ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.",
      getStarted: "Commencez par créer votre premier service.",
      createFirstService: "Créer votre premier service",
      failedToLoad: "Échec du chargement des services"
    }
  };

  const currentLang = currentLanguage as keyof typeof serviceTranslations;
  const st = (key: keyof typeof serviceTranslations.ar) =>
    serviceTranslations[currentLang]?.[key] || serviceTranslations.en[key] || key;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title={st('failedToLoad')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      {/* Services Grid */}
      {services && Array.isArray(services) && services.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service: Service) => (
            <ServiceCard
              key={service.id}
              service={service}
              onEdit={() => onEditService(service)}
              onDelete={() => onDeleteService(service.id)}
              isDeleting={deleteServiceMutation.isPending}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {st('noServicesFound')}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {Object.keys(filters).length > 0
              ? st('noServicesFiltered')
              : st('getStarted')
            }
          </p>
          {Object.keys(filters).length === 0 && (
            <Button onClick={onCreateService}>
              {st('createFirstService')}
            </Button>
          )}
        </div>
      )}
    </>
  );
});

export default function ServicesManagement() {
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [filters, setFilters] = useState<ServiceFilters>({});
  const [modalType, setModalType] = useState<'service' | 'category' | null>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const confirmation = useConfirmation();
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();

  // Custom translations for filter labels
  const filterTranslations = {
    ar: {
      search: "البحث",
      status: "الحالة",
      searchPlaceholder: "البحث في الخدمات...",
      allServices: "جميع الخدمات",
      public: "عامة",
      private: "خاصة"
    },
    en: {
      search: "Search",
      status: "Status",
      searchPlaceholder: "Search services...",
      allServices: "All Services",
      public: "Public",
      private: "Private"
    },
    fr: {
      search: "Rechercher",
      status: "Statut",
      searchPlaceholder: "Rechercher des services...",
      allServices: "Tous les services",
      public: "Public",
      private: "Privé"
    }
  };

  const currentLang = currentLanguage as keyof typeof filterTranslations;
  const ft = (key: keyof typeof filterTranslations.ar) =>
    filterTranslations[currentLang]?.[key] || filterTranslations.en[key] || key;

  // Remove useServices hook from here - it's now in ServiceResults component
  const { data: categories } = useServiceCategories();
  const deleteServiceMutation = useDeleteService();

  const handleCreateService = () => {
    setEditingService(null);
    setModalType('service');
    openModal();
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setModalType('service');
    openModal();
  };

  const handleDeleteService = async (id: number) => {
    const confirmed = await confirmation.confirm({
      title: 'Delete Service',
      message: `Are you sure you want to delete this service? This action cannot be undone and will remove all associated data.`,
      confirmText: 'Delete Service',
      cancelText: 'Cancel',
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteServiceMutation.mutateAsync(id);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleOpenCategoryManager = () => {
    setModalType('category');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingService(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<ServiceFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <>
      <PageMeta
        title="Services Management | Provider Dashboard"
        description="Manage your services, pricing, and categories"
      />
      <PageBreadcrumb pageTitle={tCommon('navigation.services')} />

      <div className="space-y-6">
        {/* Usage Warning Banner */}
        {/* <UsageWarningBanner /> */}

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('services.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('services.description')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleOpenCategoryManager}
              variant="outline"
              size="sm"
            >
              {t('services.manageCategories')}
            </Button>
            <CreateServiceButton
              onCreateService={handleCreateService}
              size="sm"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('filters.category', 'Category')}
              </label>
              <select
                value={filters.categoryId || ''}
                onChange={(e) => handleFilterChange({
                  categoryId: e.target.value ? Number(e.target.value) : undefined
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{t('filters.allCategories', 'All Categories')}</option>
                {categories && Array.isArray(categories) && categories.map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {String(category.title)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('filters.deliveryType', 'Delivery Type')}
              </label>
              <select
                value={filters.deliveryType || ''}
                onChange={(e) => handleFilterChange({
                  deliveryType: e.target.value as any || undefined
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{t('filters.allTypes', 'All Types')}</option>
                <option value="at_location">{t('filters.atLocation', 'At Location')}</option>
                <option value="at_customer">{t('filters.atCustomer', 'At Customer')}</option>
                <option value="both">{t('filters.both', 'Both')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('status')}
              </label>
              <select
                value={filters.isPublic !== undefined ? (filters.isPublic ? 'public' : 'private') : ''}
                onChange={(e) => handleFilterChange({
                  isPublic: e.target.value === 'public' ? true : e.target.value === 'private' ? false : undefined
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{ft('allServices')}</option>
                <option value="public">{ft('public')}</option>
                <option value="private">{ft('private')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('search')}
              </label>
              <input
                type="text"
                placeholder={ft('searchPlaceholder')}
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Services Grid - Now handled by ServiceResults component */}
        <ServiceResults
          filters={filters}
          onEditService={handleEditService}
          onDeleteService={handleDeleteService}
          onCreateService={handleCreateService}
          deleteServiceMutation={deleteServiceMutation}
        />
      </div>

      {/* Service Modal */}
      <Modal
        isOpen={isOpen}
        onClose={handleCloseModal}
        className="max-w-[800px] p-0"
      >
        {true ? (
          <ServiceForm
            service={editingService}
            onClose={handleCloseModal}
            onSuccess={handleSuccess}
          />
        ) : modalType === 'category' ? (
          <ServiceCategoryManager
            onClose={handleCloseModal}
          />
        ) : null}
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </>
  );
}
