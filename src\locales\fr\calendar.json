{"title": "<PERSON><PERSON><PERSON>", "appointmentCalendar": "Calendrier des rendez-vous", "subtitle": "<PERSON><PERSON><PERSON> vos rendez-vous et votre planning", "newAppointment": "Nouveau rendez-vous", "editAppointment": "Modifier le rendez-vous", "appointmentDetails": "<PERSON><PERSON><PERSON> du rendez-vous", "scheduleAppointment": "Planifier un nouveau rendez-vous", "updateAppointment": "Mettre à jour les détails du rendez-vous", "views": {"month": "<PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "day": "Jour"}, "today": "<PERSON><PERSON><PERSON>'hui", "list": "Liste", "timeSlots": {"5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "20minutes": "20 minutes", "25minutes": "25 minutes", "30minutes": "30 minutes", "35minutes": "35 minutes", "40minutes": "40 minutes", "45minutes": "45 minutes", "50minutes": "50 minutes", "55minutes": "55 minutes", "1hour": "1 heure"}, "status": {"pending": "En attente", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "inProgress": "En cours", "noShow": "Absent"}, "statusDescriptions": {"pending": "<PERSON><PERSON><PERSON> de rendez-vous reçue", "confirmed": "<PERSON><PERSON><PERSON>vous confirmé par le prestataire", "completed": "<PERSON><PERSON><PERSON>vous terminé avec succès", "cancelled": "<PERSON><PERSON><PERSON>vous annulé", "noShow": "Le client ne s'est pas présenté"}, "tooltip": {"duration": "<PERSON><PERSON><PERSON>", "minutes": "minutes", "moreAppointments": "rendez-vous de plus", "moreAppointmentsPlural": "rendez-vous de plus"}, "sidebar": {"title": "Paramètres du calendrier", "timeRange": "<PERSON>lage horaire", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "timeSlotInterval": "Intervalle de créneaux", "services": "Services", "allServices": "Tous les services", "newService": "+ Nouveau service", "selectAll": "<PERSON><PERSON>", "clearAll": "Tout effacer", "statuses": "Statuts des rendez-vous", "allStatuses": "Tous les statuts"}, "appointment": {"customer": "Client", "service": "Service", "location": "<PERSON><PERSON>", "queue": "File d'attente", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "duration": "<PERSON><PERSON><PERSON> (minutes)", "notes": "Notes", "slots": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "selectCustomer": "Veuillez sélectionner un client", "selectService": "Veuillez sélectionner un service", "selectLocation": "Veuillez sélectionner un lieu", "selectQueue": "<PERSON><PERSON><PERSON>z sélectionner une file d'attente", "selectStartTime": "Veuillez sélectionner l'heure de début", "endTimeRequired": "L'heure de fin est requise", "durationRequired": "La durée du service est requise", "slotsRequired": "Au moins 1 créneau est requis", "autoSetFromService": "(Défini automatiquement par le service)", "selectServiceForDuration": "Sélectionner un service pour définir la durée", "addNotesPlaceholder": "Ajouter des notes ou des instructions spéciales..."}, "customer": {"newCustomer": "Nouveau client", "existingCustomer": "Client existant", "firstName": "Prénom", "lastName": "Nom de famille", "email": "E-mail", "phone": "Téléphone", "createAndSelect": "<PERSON><PERSON><PERSON> et sélectionner", "selectCustomer": "Sélectionner un client...", "searchCustomers": "Rechercher des clients...", "noCustomersFound": "Aucun client trouvé", "createNewCustomer": "<PERSON><PERSON>er un nouveau client", "cancel": "Annuler", "newCustomerInfo": "Informations du nouveau client", "enterFirstName": "En<PERSON>r le prénom", "enterLastName": "Entrer le nom de famille", "enterEmail": "Entrer l'adresse e-mail", "enterPhone": "Entrer le numéro de téléphone", "createCustomer": "<PERSON><PERSON><PERSON> le <PERSON>", "creating": "Création en cours..."}, "actions": {"start": "Commencer", "complete": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "createAppointment": "<PERSON><PERSON><PERSON> un rendez-vous", "updateAppointment": "Mettre à jour le rendez-vous", "creating": "Création...", "updating": "Mise à jour..."}, "form": {"selectService": "Sélectionner un service", "selectLocation": "Sélectionner un lieu", "selectQueue": "Sé<PERSON><PERSON>ner une file d'attente"}}