apiVersion: apps/v1
kind: Deployment
metadata:
  name: dalti-frontend
  namespace: yachfin-medical-system-tests
  labels:
    app: dalti-frontend
spec:
  replicas: 1 # Start with 2 replicas for availability
  selector:
    matchLabels:
      app: dalti-frontend
  template:
    metadata:
      labels:
        app: dalti-frontend
    spec:
      nodeSelector:
        node-type: worker # Match worker nodes like the API
      imagePullSecrets:
        - name: my-dockerhub-secret # If frontend image is private
      containers:
        - name: dalti-frontend-nginx
          image: kotoubm7/dalti-frontend:beta-1.0.0
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              name: http
          env:
            - name: VITE_API_BASE_URL_PROD
              value: "https://dapi.adscloud.org"
          volumeMounts:
            - name: nginx-config-volume
              mountPath: /etc/nginx/conf.d/default.conf # Mount custom config
              subPath: nginx.conf # Mount only the nginx.conf key from the ConfigMap
              readOnly: true
          resources: # Add basic resource requests/limits
            requests:
              memory: "64Mi"
              cpu: "50m"
            limits:
              memory: "128Mi"
              cpu: "100m"
          # Optional: Add simple readiness/liveness probes for Nginx
          readinessProbe:
            httpGet:
              path: / # Check if root path is served
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: http # Check if port 80 is open
            initialDelaySeconds: 15
            periodSeconds: 20
      volumes:
        - name: nginx-config-volume
          configMap:
            name: dalti-frontend-nginx-conf # Reference the ConfigMap created earlier 