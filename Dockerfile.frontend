# Stage 1: Use a lightweight Nginx image
FROM nginx:1.25-alpine AS frontend-server

# Define web root inside the container
ARG NGINX_WEB_ROOT=/usr/share/nginx/html

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Copy the pre-built static frontend assets
# IMPORTANT: Assumes this Docker build is run AFTER the frontend has been built
# in ./app/.wasp/build/web-app/build/
COPY ./dist ${NGINX_WEB_ROOT}

# Ensure Nginx can read the files
RUN chown -R nginx:nginx ${NGINX_WEB_ROOT} && chmod -R 755 ${NGINX_WEB_ROOT}

# Expose port 80
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"] 