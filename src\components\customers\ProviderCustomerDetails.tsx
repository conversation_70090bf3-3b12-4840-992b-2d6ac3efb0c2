import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { ProviderCustomer } from '../../types/provider-customer';
import { useCommonTranslation, useManagementTranslation } from '../../hooks/useTranslation';
import { formatLocalDateTimeWithLocale } from '../../utils/timezone';
import {
  XMarkIcon,
  UserCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  IdentificationIcon,
  CalendarIcon,
  ClockIcon,
  ChartBarIcon
} from '../../icons';

interface ProviderCustomerDetailsProps {
  customer: ProviderCustomer;
  onClose: () => void;
  onEdit: () => void;
}

export default function ProviderCustomerDetails({ customer, onClose, onEdit }: ProviderCustomerDetailsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'appointments'>('overview');
  const { t: tCommon, currentLanguage } = useCommonTranslation();
  const { t } = useManagementTranslation();

  const getInitials = () => {
    const firstInitial = customer.firstName?.charAt(0)?.toUpperCase() || '';
    const lastInitial = customer.lastName?.charAt(0)?.toUpperCase() || '';
    return `${firstInitial}${lastInitial}`;
  };

  const formatDate = (dateString: string) => {
    return formatLocalDateTimeWithLocale(dateString, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }, currentLanguage);
  };

  const getStatusColor = () => {
    return customer.isActive !== false 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  const getStatusText = () => {
    return customer.isActive !== false
      ? tCommon('status.active', 'Active')
      : tCommon('status.inactive', 'Inactive');
  };

  const tabs = [
    { id: 'overview', label: t('customers.details.overview', 'Overview'), icon: UserCircleIcon },
    { id: 'appointments', label: tCommon('navigation.appointments', 'Appointments'), icon: CalendarIcon },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl w-full max-h-[85vh] overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
            {getInitials()}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {customer.firstName} {customer.lastName}
            </h2>
            <div className="flex items-center space-x-3 mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
                {getStatusText()}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {t('customers.details.customerSince', 'Customer since {{date}}', { date: formatDate(customer.createdAt) })}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            onClick={onEdit}
            variant="outline"
            size="sm"
          >
            {t('customers.details.editCustomer', 'Edit Customer')}
          </Button>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6 overflow-y-auto max-h-[calc(85vh-200px)]">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('customers.details.contactInformation', 'Contact Information')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <PhoneIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {tCommon('common.phone', 'Mobile Number')}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {customer.mobileNumber}
                    </div>
                  </div>
                </div>

                {customer.email && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <EnvelopeIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {tCommon('common.email', 'Email Address')}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {customer.email}
                      </div>
                    </div>
                  </div>
                )}

                {customer.nationalId && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <IdentificationIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {t('customers.details.nationalId', 'National ID')}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {customer.nationalId}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Statistics */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('customers.details.customerStatistics', 'Customer Statistics')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <CalendarIcon className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {customer.appointmentCount}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {t('customers.details.totalAppointments', 'Total Appointments')}
                  </div>
                </div>

                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <ClockIcon className="w-8 h-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {getStatusText()}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {t('customers.details.relationshipStatus', 'Relationship Status')}
                  </div>
                </div>

                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <ChartBarIcon className="w-8 h-8 text-purple-600 dark:text-purple-400 mx-auto mb-2" />
                  <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                    {formatDate(customer.createdAt).split(',')[0]}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {t('customers.details.customerSinceLabel', 'Customer Since')}
                  </div>
                </div>
              </div>
            </div>

            {/* Provider Notes */}
            {customer.notes && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('customers.details.providerNotes', 'Provider Notes')}
                </h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {customer.notes}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'appointments' && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('customers.details.appointmentHistory', 'Appointment History')}
            </h3>
            <div className="text-center py-12">
              <CalendarIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('customers.details.appointmentHistory', 'Appointment History')}
              </h4>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {t('customers.details.appointmentHistoryDescription', 'Detailed appointment history will be displayed here when integrated with the appointments system.')}
              </p>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {t('customers.details.totalAppointmentsCount', 'Total Appointments: {{count}}', { count: customer.appointmentCount })}
              </div>
            </div>
          </div>
        )}


      </div>
    </div>
  );
}
