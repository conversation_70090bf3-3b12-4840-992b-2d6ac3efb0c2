import React, { useState, useEffect } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { useManagementTranslation, useCommonTranslation } from "../../hooks/useTranslation";
import { useAuth } from "../../context/AuthContext";
import { useRTL } from "../../context/LanguageContext";
import ServicesTab from './tabs/ServicesTab';
import LocationsTab from './tabs/LocationsTab';
import QueuesTab from './tabs/QueuesTab';

type TabType = 'services' | 'locations' | 'queues';

export default function BusinessSetup() {
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { fetchSubscriptionData, fetchUsedCreditsData } = useAuth();
  const { isRTL, direction } = useRTL();
  const [activeTab, setActiveTab] = useState<TabType>('services');

  // Fetch subscription data when component mounts to ensure subscription guards work correctly
  useEffect(() => {
    console.log("Business Setup page reload - fetching subscription data");
    fetchSubscriptionData();
    fetchUsedCreditsData();
  }, []);

  const tabs = [
    {
      id: 'services' as TabType,
      label: tCommon('navigation.services'),
      description: t('businessSetup.servicesDesc', 'Manage your service offerings and pricing'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
    },
    {
      id: 'locations' as TabType,
      label: tCommon('navigation.locations'),
      description: t('businessSetup.locationsDesc', 'Set up your business locations and operating hours'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    },
    {
      id: 'queues' as TabType,
      label: tCommon('navigation.queues'),
      description: t('businessSetup.queuesDesc', 'Configure appointment queues and waiting systems'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'services':
        return <ServicesTab />;
      case 'locations':
        return <LocationsTab />;
      case 'queues':
        return <QueuesTab />;
      default:
        return <ServicesTab />;
    }
  };

  return (
    <>
      <PageMeta
        title={`${t('businessSetup.title', 'Business Setup')} | ${tCommon('common.appName', 'Dalti Provider')}`}
        description={t('businessSetup.description', 'Configure your business services, locations, and appointment queues')}
      />

      <div className="space-y-6" dir={direction}>
        {/* Page Header */}
        <div>
          <PageBreadcrumb
            items={[
              { label: tCommon('navigation.dashboard'), href: '/' },
              { label: t('businessSetup.title', 'Business Setup') }
            ]}
          />
          
          <div className="mt-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('businessSetup.title', 'Business Setup')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('businessSetup.subtitle', 'Configure your business services, locations, and appointment queues')}
            </p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex px-6" aria-label="Tabs" dir={direction}>
              {tabs.map((tab, index) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  } ${
                    index < tabs.length - 1
                      ? isRTL ? 'ml-8' : 'mr-8'
                      : ''
                  }`}
                >
                  <span className={`${activeTab === tab.id ? 'text-brand-500' : 'text-gray-400 group-hover:text-gray-500'} ${isRTL ? 'ml-2' : 'mr-2'}`}>
                    {tab.icon}
                  </span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Description */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50" dir={direction}>
            <div className="flex items-center">
              <span className={`text-brand-500 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                {tabs.find(tab => tab.id === activeTab)?.icon}
              </span>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {tabs.find(tab => tab.id === activeTab)?.description}
              </p>
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6" dir={direction}>
            {renderTabContent()}
          </div>
        </div>
      </div>
    </>
  );
}
