import React from 'react';
import Button from '../ui/button/Button';
import { Appointment } from '../../types';
import {
  useUpdateAppointmentStatus,
} from '../../hooks/useAppointments';
import { formatLocalDateWithLocale, formatLocalTimeWithLocale, getLocaleFromLanguage } from '../../utils/timezone';
import { useNavigate } from 'react-router';
import ConfirmationDialog from '../ui/confirmation/ConfirmationDialog';
import { useConfirmation } from '../../hooks/useConfirmation';
import { useCommonTranslation, useCalendarTranslation } from '../../hooks/useTranslation';

interface AppointmentCardProps {
  appointment: Appointment;
  onView: () => void;
}

export default function AppointmentCard({
  appointment,
  onView
}: AppointmentCardProps) {
  const navigate = useNavigate();
  const updateStatusMutation = useUpdateAppointmentStatus();
  const confirmation = useConfirmation();
  const { t: tCommon, currentLanguage } = useCommonTranslation();
  const { t: tCalendar } = useCalendarTranslation();

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: formatLocalDateWithLocale(dateTime, {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      }, currentLanguage),
      time: formatLocalTimeWithLocale(dateTime, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }, currentLanguage),
      dayOfWeek: date.toLocaleDateString(getLocaleFromLanguage(currentLanguage), { weekday: 'short' }),
    };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'no-show':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      default:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'completed':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const handleQuickConfirm = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'confirmed',
          notes: 'Appointment confirmed by provider'
        }
      });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleQuickCancel = async (e: React.MouseEvent) => {
    e.stopPropagation();

    const confirmed = await confirmation.confirm({
      title: tCommon('actions.cancel') + ' ' + tCommon('navigation.appointment'),
      message: `Are you sure you want to cancel the appointment with ${appointment.customer?.firstName} ${appointment.customer?.lastName}? This action cannot be undone.`,
      confirmText: tCommon('actions.cancel') + ' ' + tCommon('navigation.appointment'),
      cancelText: 'Keep Appointment',
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await updateStatusMutation.mutateAsync({
          id: appointment.id,
          status: {
            status: 'canceled',
            notes: 'Cancelled by provider'
          }
        });
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleQuickComplete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'completed',
          notes: 'Appointment completed'
        }
      });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleStartAppointment = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'InProgress',
          notes: t('appointments.details.appointmentStarted', 'Appointment started')
        }
      });
      // Navigate to service session page
      navigate(`/service-session/${appointment.id}`);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const { date, time, dayOfWeek } = formatDateTime(appointment.expectedAppointmentStartTime);
  const isLoading = updateStatusMutation.isPending;

  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={onView}
    >
      <div className="flex items-start justify-between">
        {/* Left side - Main info */}
        <div className="flex items-start space-x-4 flex-1">
          {/* Date/Time */}
          <div className="text-center min-w-[80px]">
            <div className="text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wide">
              {dayOfWeek}
            </div>
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {date.split(' ')[1]} {/* Day */}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {date.split(' ')[0]} {/* Month */}
            </div>
            <div className="text-sm font-medium text-brand-600 dark:text-brand-400 mt-1">
              {time}
            </div>
          </div>

          {/* Customer & Service Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm flex-shrink-0">
                {appointment.customer?.firstName?.charAt(0)}{appointment.customer?.lastName?.charAt(0)}
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  {appointment.customer?.firstName} {appointment.customer?.lastName}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {appointment.customer?.email}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {appointment.service?.title}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  ({appointment.service?.duration}min)
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {appointment.place?.name}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${appointment.service?.price}
                </span>
              </div>
            </div>

            {appointment.notes && (
              <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">{tCommon('common.notes')}:</span> {appointment.notes}
              </div>
            )}
          </div>
        </div>

        {/* Right side - Status and Actions */}
        <div className="flex flex-col items-end space-y-3 ml-4">
          {/* Status */}
          <span
            className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
              appointment.status
            )}`}
          >
            <span className="mr-1">
              {getStatusIcon(appointment.status)}
            </span>
            {tCommon(`status.${appointment.status}`)}
          </span>

          {/* Quick Actions */}
          <div className="flex flex-col space-y-2">
            {appointment.status === 'pending' && (
              <Button
                onClick={handleQuickConfirm}
                size="sm"
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-xs"
              >
                {updateStatusMutation.isPending ? tCommon('actions.loading') : tCommon('actions.confirm')}
              </Button>
            )}

            {appointment.status === 'confirmed' && (
              <Button
                onClick={handleStartAppointment}
                size="sm"
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-xs"
              >
                {updateStatusMutation.isPending ? tCommon('actions.loading') : tCalendar('actions.start', 'Start')}
              </Button>
            )}

            {(appointment.status === 'pending' || appointment.status === 'confirmed') && (
              <Button
                onClick={handleQuickComplete}
                size="sm"
                disabled={isLoading}
                className="bg-purple-600 hover:bg-purple-700 text-xs"
              >
                {updateStatusMutation.isPending ? tCommon('actions.loading') : tCalendar('actions.complete', 'Complete')}
              </Button>
            )}

            {(appointment.status === 'pending' || appointment.status === 'confirmed') && (
              <Button
                onClick={handleQuickCancel}
                variant="outline"
                size="sm"
                disabled={isLoading}
                className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20 text-xs"
              >
                {updateStatusMutation.isPending ? tCommon('actions.loading') : tCommon('actions.cancel')}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
