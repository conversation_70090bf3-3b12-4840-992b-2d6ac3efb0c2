import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../../context/LanguageContext';
import { ChevronDownIcon } from '../../icons';

interface LanguageSwitcherProps {
  variant?: 'header' | 'sidebar' | 'standalone';
  showLabel?: boolean;
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'header',
  showLabel = true,
  className = '',
}) => {
  const { currentLanguage, languageInfo, supportedLanguages, changeLanguage, isChangingLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLanguageChange = async (languageCode: typeof currentLanguage) => {
    setIsOpen(false);
    await changeLanguage(languageCode);
  };

  // Variant-specific styling
  const getVariantStyles = () => {
    switch (variant) {
      case 'header':
        return {
          button: 'relative flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white',
          dropdown: 'absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      case 'sidebar':
        return {
          button: 'flex items-center gap-3 w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors',
          dropdown: 'absolute left-full top-0 ml-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      case 'standalone':
        return {
          button: 'flex items-center gap-2 px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all',
          dropdown: 'absolute right-0 top-full mt-2 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      default:
        return {
          button: '',
          dropdown: '',
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Language Switcher Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isChangingLanguage}
        className={`${styles.button} ${isChangingLanguage ? 'opacity-50 cursor-not-allowed' : ''}`}
        aria-label="Change language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {variant === 'header' ? (
          /* Header variant - only flag */
          <span className="text-lg" role="img" aria-label={languageInfo.name}>
            {languageInfo.flag}
          </span>
        ) : (
          /* Other variants - flag + label + chevron */
          <>
            {/* Flag Icon */}
            <span className="text-lg" role="img" aria-label={languageInfo.name}>
              {languageInfo.flag}
            </span>

            {/* Language Label */}
            {showLabel && (
              <span className="hidden sm:inline">
                {variant === 'sidebar' ? languageInfo.nativeName : languageInfo.code.toUpperCase()}
              </span>
            )}

            {/* Loading indicator or chevron */}
            {isChangingLanguage ? (
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            ) : (
              <ChevronDownIcon
                className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              />
            )}
          </>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className={styles.dropdown}>
          <div className="py-2">
            {supportedLanguages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                disabled={isChangingLanguage}
                className={`
                  w-full flex items-center gap-3 px-4 py-2 text-sm text-left
                  ${language.code === currentLanguage 
                    ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' 
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                  ${isChangingLanguage ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-900 dark:hover:text-white'}
                  transition-colors
                `}
                aria-label={`Switch to ${language.name}`}
              >
                {/* Flag */}
                <span className="text-lg" role="img" aria-label={language.name}>
                  {language.flag}
                </span>
                
                {/* Language Info */}
                <div className="flex-1">
                  <div className="font-medium">{language.nativeName}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {language.name}
                  </div>
                </div>
                
                {/* Current Language Indicator */}
                {language.code === currentLanguage && (
                  <div className="w-2 h-2 bg-brand-500 rounded-full" />
                )}
              </button>
            ))}
          </div>
          
          {/* Footer */}
          <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Language preference is saved automatically
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Compact version for mobile or tight spaces
export const CompactLanguageSwitcher: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <LanguageSwitcher
      variant="header"
      showLabel={false}
      className={className}
    />
  );
};

// Sidebar version with full labels
export const SidebarLanguageSwitcher: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <LanguageSwitcher
      variant="sidebar"
      showLabel={true}
      className={className}
    />
  );
};

export default LanguageSwitcher;
