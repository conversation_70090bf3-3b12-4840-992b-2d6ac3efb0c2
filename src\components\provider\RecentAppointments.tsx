import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTimeWithLocale, getLocaleFromLanguage } from '../../utils/timezone';
import { useCommonTranslation } from '../../hooks/useTranslation';

// Mock data - will be replaced with real API data
const mockRecentAppointments = [
  {
    id: 1,
    customer: { firstName: 'Sarah', lastName: '<PERSON>' },
    service: { title: 'Hair Cut & Style', price: 75 },
    expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
    status: 'completed',
    realAppointmentEndTime: '2024-01-15T11:00:00Z'
  },
  {
    id: 2,
    customer: { firstName: 'Mike', lastName: 'Davis' },
    service: { title: 'Color Treatment', price: 150 },
    expectedAppointmentStartTime: '2024-01-15T14:00:00Z',
    status: 'completed',
    realAppointmentEndTime: '2024-01-15T16:00:00Z'
  },
  {
    id: 3,
    customer: { firstName: 'Emily', lastName: 'Chen' },
    service: { title: 'Consultation', price: 25 },
    expectedAppointmentStartTime: '2024-01-16T09:30:00Z',
    status: 'completed',
    realAppointmentEndTime: '2024-01-16T10:00:00Z'
  },
  {
    id: 4,
    customer: { firstName: 'John', lastName: 'Smith' },
    service: { title: 'Deep Conditioning', price: 60 },
    expectedAppointmentStartTime: '2024-01-16T11:00:00Z',
    status: 'completed',
    realAppointmentEndTime: '2024-01-16T11:45:00Z'
  },
  {
    id: 5,
    customerName: 'Lisa Wilson',
    service: 'Styling Session',
    date: '2024-01-16',
    time: '3:30 PM',
    status: 'cancelled',
    duration: 90,
    price: 100,
    location: 'Downtown Branch',
  },
];

export default function RecentAppointments() {
  // Fetch real appointments data
  const { data: appointments } = useAppointments();
  const { t, currentLanguage } = useCommonTranslation();

  // Use real data if available, otherwise fall back to mock data
  const displayAppointments = appointments?.slice(0, 5) || mockAppointments;
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'completed':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return t('common.today', 'Today');
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return t('common.tomorrow', 'Tomorrow');
    } else {
      const locale = getLocaleFromLanguage(currentLanguage);
      return date.toLocaleDateString(locale, {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
              Recent Appointments
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Latest bookings and their status
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {/* Navigate to appointments */}}
          >
            View All
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {displayAppointments.map((appointment) => {
          // Handle both real API data and mock data
          const isRealData = 'expectedAppointmentStartTime' in appointment;
          const appointmentData = isRealData ? {
            id: appointment.id,
            customerName: `${appointment.customer?.firstName || ''} ${appointment.customer?.lastName || ''}`.trim(),
            service: appointment.service?.title || 'Service',
            date: appointment.expectedAppointmentStartTime,
            time: formatLocalTimeWithLocale(appointment.expectedAppointmentStartTime, {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }, currentLanguage),
            status: appointment.status,
            duration: appointment.service?.duration || 60,
            price: appointment.service?.price || 0,
            location: appointment.place?.name || 'Location',
          } : appointment;

          return (
          <div
            key={appointmentData.id}
            className="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-brand-300 dark:hover:border-brand-600 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  {appointmentData.customerName.split(' ').map(n => n[0]).join('')}
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {appointmentData.customerName}
                  </p>
                  <span
                    className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                      appointmentData.status
                    )}`}
                  >
                    <span className="mr-1">
                      {getStatusIcon(appointmentData.status)}
                    </span>
                    {appointmentData.status}
                  </span>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  {appointmentData.service}
                </p>
                
                <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {isRealData ? formatDate(appointmentData.date) : formatDate(appointmentData.date)}
                  </span>
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {appointmentData.time}
                  </span>
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {appointmentData.location}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  ${appointmentData.price}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {appointmentData.duration}min
                </p>
              </div>
              
              <div className="flex space-x-1">
                <button className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </button>
                <button className="p-1 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        );
        })}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-lg font-bold text-green-600 dark:text-green-400">
              {displayAppointments.filter(a => a.status === 'confirmed').length}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Confirmed
            </p>
          </div>
          <div>
            <p className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
              {displayAppointments.filter(a => a.status === 'pending').length}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Pending
            </p>
          </div>
          <div>
            <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {displayAppointments.filter(a => a.status === 'completed').length}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Completed
            </p>
          </div>
          <div>
            <p className="text-lg font-bold text-red-600 dark:text-red-400">
              {displayAppointments.filter(a => a.status === 'cancelled').length}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Cancelled
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
