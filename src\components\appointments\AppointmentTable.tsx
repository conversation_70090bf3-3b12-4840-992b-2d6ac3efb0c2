import React from 'react';
import { Appointment } from '../../types';
import { Table, TableHeader, TableBody, TableRow, TableCell } from '../ui/table';
import { formatLocalDateWithLocale, formatLocalTimeWithLocale, getLocaleFromLanguage } from '../../utils/timezone';
import { useCommonTranslation, useManagementTranslation } from '../../hooks/useTranslation';
import Button from '../ui/button/Button';
import { EyeIcon, CalendarIcon, UserIcon, ClockIcon } from '../../icons';

interface AppointmentTableProps {
  appointments: Appointment[];
  onViewAppointment: (appointment: Appointment) => void;
}

export default function AppointmentTable({ appointments, onViewAppointment }: AppointmentTableProps) {
  const { t: tCommon, currentLanguage } = useCommonTranslation();
  const { t } = useManagementTranslation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'InProgress':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'noshow':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'no-show':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      default:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: formatLocalDateWithLocale(dateTime, {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      }, currentLanguage),
      time: formatLocalTimeWithLocale(dateTime, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }, currentLanguage),
      dayOfWeek: date.toLocaleDateString(getLocaleFromLanguage(currentLanguage), { weekday: 'short' }),
    };
  };

  const getCustomerInitials = (customer: any) => {
    const firstInitial = customer?.firstName?.charAt(0)?.toUpperCase() || '';
    const lastInitial = customer?.lastName?.charAt(0)?.toUpperCase() || '';
    return `${firstInitial}${lastInitial}`;
  };

  if (!appointments || appointments.length === 0) {
    return (
      <div className="text-center py-12">
        <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          {t('appointments.noAppointments', 'No appointments found')}
        </h3>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <div className="max-w-full overflow-x-auto">
        <Table>
          {/* Table Header */}
          <TableHeader className="border-b border-gray-100 dark:border-gray-700">
            <TableRow>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {t('appointments.details.customerInformation', 'Customer')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {t('appointments.details.service', 'Service')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {tCommon('common.date')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {tCommon('common.time')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {tCommon('common.status', 'Status')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-start text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {t('appointments.details.location', 'Location')}
              </TableCell>
              <TableCell
                isHeader
                className="px-6 py-3 font-medium text-gray-500 text-end text-xs uppercase tracking-wider dark:text-gray-400"
              >
                {tCommon('common.actions')}
              </TableCell>
            </TableRow>
          </TableHeader>

          {/* Table Body */}
          <TableBody className="divide-y divide-gray-100 dark:divide-gray-700">
            {appointments.map((appointment) => {
              const { date, time, dayOfWeek } = formatDateTime(appointment.expectedAppointmentStartTime);
              
              return (
                <TableRow 
                  key={appointment.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                >
                  {/* Customer */}
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                        {getCustomerInitials(appointment.customer)}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {appointment.customer?.firstName} {appointment.customer?.lastName}
                        </div>
                        {appointment.customer?.phone && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {appointment.customer.phone}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>

                  {/* Service */}
                  <TableCell className="px-6 py-4">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {appointment.service?.title || 'N/A'}
                      </div>
                      {appointment.service?.duration && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {appointment.service.duration} {t('appointments.details.minutes', 'minutes')}
                        </div>
                      )}
                    </div>
                  </TableCell>

                  {/* Date */}
                  <TableCell className="px-6 py-4">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {date}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {dayOfWeek}
                      </div>
                    </div>
                  </TableCell>

                  {/* Time */}
                  <TableCell className="px-6 py-4">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {time}
                    </div>
                  </TableCell>

                  {/* Status */}
                  <TableCell className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                      {tCommon(`status.${appointment.status}`, appointment.status)}
                    </span>
                  </TableCell>

                  {/* Location */}
                  <TableCell className="px-6 py-4">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {appointment.location?.name || 'N/A'}
                    </div>
                  </TableCell>

                  {/* Actions */}
                  <TableCell className="px-6 py-4 text-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewAppointment(appointment)}
                      className="text-brand-600 border-brand-300 hover:bg-brand-50 dark:text-brand-400 dark:border-brand-800 dark:hover:bg-brand-900/20"
                    >
                      {/* <EyeIcon className="w-4 h-4 me-1" /> */}
                      {tCommon('actions.view')}
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
