/**
 * Timezone utility functions for handling local time display and UTC API submission
 */

/**
 * Convert a local datetime string to UTC ISO string for API submission
 * @param localDateTimeString - Local datetime string from datetime-local input (e.g., "2024-01-15T14:30")
 * @returns UTC ISO string (e.g., "2024-01-15T19:30:00.000Z")
 */
export function localDateTimeToUTC(localDateTimeString: string): string {
  if (!localDateTimeString) return '';
  
  // Create a Date object from the local datetime string
  // The datetime-local input provides a string in format "YYYY-MM-DDTHH:mm"
  // When we create a Date from this, it's interpreted as local time
  const localDate = new Date(localDateTimeString);
  
  // Return the UTC ISO string
  return localDate.toISOString();
}

/**
 * Convert a UTC ISO string to local datetime string for form display
 * @param utcISOString - UTC ISO string from API (e.g., "2024-01-15T19:30:00.000Z")
 * @returns Local datetime string for datetime-local input (e.g., "2024-01-15T14:30")
 */
export function utcToLocalDateTime(utcISOString: string): string {
  if (!utcISOString) return '';
  
  // Create a Date object from the UTC string
  const utcDate = new Date(utcISOString);
  
  // Get the local time components
  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, '0');
  const day = String(utcDate.getDate()).padStart(2, '0');
  const hours = String(utcDate.getHours()).padStart(2, '0');
  const minutes = String(utcDate.getMinutes()).padStart(2, '0');
  
  // Return in datetime-local format
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Format a UTC ISO string for local display
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @param locale - Locale string (e.g., 'en-US', 'ar-SA', 'fr-FR')
 * @returns Formatted local time string
 */
export function formatLocalDateTime(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  },
  locale: string = 'en-US'
): string {
  if (!utcISOString) return '';

  const date = new Date(utcISOString);
  return date.toLocaleDateString(locale, options);
}

/**
 * Format a UTC ISO string for local time display only
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for time formatting
 * @param locale - Locale string (e.g., 'en-US', 'ar-SA', 'fr-FR')
 * @returns Formatted local time string
 */
export function formatLocalTime(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  },
  locale: string = 'en-US'
): string {
  if (!utcISOString) return '';

  const date = new Date(utcISOString);
  return date.toLocaleTimeString(locale, options);
}

/**
 * Format a UTC ISO string for local date display only
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for date formatting
 * @param locale - Locale string (e.g., 'en-US', 'ar-SA', 'fr-FR')
 * @returns Formatted local date string
 */
export function formatLocalDate(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  },
  locale: string = 'en-US'
): string {
  if (!utcISOString) return '';

  const date = new Date(utcISOString);
  return date.toLocaleDateString(locale, options);
}

/**
 * Get the user's timezone
 * @returns The user's timezone identifier (e.g., "America/New_York")
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Get the user's timezone offset in minutes
 * @returns Timezone offset in minutes (negative for timezones ahead of UTC)
 */
export function getTimezoneOffset(): number {
  return new Date().getTimezoneOffset();
}

/**
 * Check if the current time is in daylight saving time
 * @returns True if currently in DST
 */
export function isDaylightSavingTime(): boolean {
  const now = new Date();
  const january = new Date(now.getFullYear(), 0, 1);
  const july = new Date(now.getFullYear(), 6, 1);
  
  return Math.max(january.getTimezoneOffset(), july.getTimezoneOffset()) !== now.getTimezoneOffset();
}

/**
 * Get locale string from language code with Gregorian calendar and Western numerals
 * @param language - Language code ('en', 'ar', 'fr')
 * @returns Locale string for date formatting (always Gregorian calendar with Western numerals)
 */
export function getLocaleFromLanguage(language: string): string {
  const localeMap: Record<string, string> = {
    'en': 'en-US',
    'ar': 'ar-SA-u-ca-gregory-nu-latn', // Force Gregorian calendar + Western numerals for Arabic
    'fr': 'fr-FR'
  };
  return localeMap[language] || 'en-US';
}

/**
 * Format a UTC ISO string for local display with current language locale
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @param currentLanguage - Current language code
 * @returns Formatted local time string
 */
export function formatLocalDateTimeWithLocale(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  },
  currentLanguage: string = 'en'
): string {
  const locale = getLocaleFromLanguage(currentLanguage);
  return formatLocalDateTime(utcISOString, options, locale);
}

/**
 * Format a UTC ISO string for local time display with current language locale
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for time formatting
 * @param currentLanguage - Current language code
 * @returns Formatted local time string
 */
export function formatLocalTimeWithLocale(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  },
  currentLanguage: string = 'en'
): string {
  const locale = getLocaleFromLanguage(currentLanguage);
  return formatLocalTime(utcISOString, options, locale);
}

/**
 * Format a UTC ISO string for local date display with current language locale
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for date formatting
 * @param currentLanguage - Current language code
 * @returns Formatted local date string
 */
export function formatLocalDateWithLocale(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  },
  currentLanguage: string = 'en'
): string {
  const locale = getLocaleFromLanguage(currentLanguage);
  return formatLocalDate(utcISOString, options, locale);
}
