import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import OpeningHoursForm from '../forms/OpeningHoursForm';
import { useCreateLocation, useUpdateLocation } from '../../hooks/useLocations';
import { Location, LocationCreateRequest } from '../../types';
import { useFormTranslation, useManagementTranslation } from '../../hooks/useTranslation';

import { getUserTimezone } from '../../utils/timezone';

// Opening hours schema matching API
const openingHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Validation schema will be created inside component to use translations

type LocationFormData = {
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile: string;
  isMobileHidden: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  openingHours?: any[];
};

interface LocationFormProps {
  location?: Location | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Days of the week - will be translated in component

const timezones = [
  // Africa
  'Africa/Abidjan',
  'Africa/Accra',
  'Africa/Addis_Ababa',
  'Africa/Algiers',
  'Africa/Asmara',
  'Africa/Bamako',
  'Africa/Bangui',
  'Africa/Banjul',
  'Africa/Bissau',
  'Africa/Blantyre',
  'Africa/Brazzaville',
  'Africa/Bujumbura',
  'Africa/Cairo',
  'Africa/Casablanca',
  'Africa/Ceuta',
  'Africa/Conakry',
  'Africa/Dakar',
  'Africa/Dar_es_Salaam',
  'Africa/Djibouti',
  'Africa/Douala',
  'Africa/El_Aaiun',
  'Africa/Freetown',
  'Africa/Gaborone',
  'Africa/Harare',
  'Africa/Johannesburg',
  'Africa/Juba',
  'Africa/Kampala',
  'Africa/Khartoum',
  'Africa/Kigali',
  'Africa/Kinshasa',
  'Africa/Lagos',
  'Africa/Libreville',
  'Africa/Lome',
  'Africa/Luanda',
  'Africa/Lubumbashi',
  'Africa/Lusaka',
  'Africa/Malabo',
  'Africa/Maputo',
  'Africa/Maseru',
  'Africa/Mbabane',
  'Africa/Mogadishu',
  'Africa/Monrovia',
  'Africa/Nairobi',
  'Africa/Ndjamena',
  'Africa/Niamey',
  'Africa/Nouakchott',
  'Africa/Ouagadougou',
  'Africa/Porto-Novo',
  'Africa/Sao_Tome',
  'Africa/Tripoli',
  'Africa/Tunis',
  'Africa/Windhoek',

  // Americas
  'America/Adak',
  'America/Anchorage',
  'America/Anguilla',
  'America/Antigua',
  'America/Araguaina',
  'America/Argentina/Buenos_Aires',
  'America/Argentina/Catamarca',
  'America/Argentina/Cordoba',
  'America/Argentina/Jujuy',
  'America/Argentina/La_Rioja',
  'America/Argentina/Mendoza',
  'America/Argentina/Rio_Gallegos',
  'America/Argentina/Salta',
  'America/Argentina/San_Juan',
  'America/Argentina/San_Luis',
  'America/Argentina/Tucuman',
  'America/Argentina/Ushuaia',
  'America/Aruba',
  'America/Asuncion',
  'America/Atikokan',
  'America/Bahia',
  'America/Bahia_Banderas',
  'America/Barbados',
  'America/Belem',
  'America/Belize',
  'America/Blanc-Sablon',
  'America/Boa_Vista',
  'America/Bogota',
  'America/Boise',
  'America/Cambridge_Bay',
  'America/Campo_Grande',
  'America/Cancun',
  'America/Caracas',
  'America/Cayenne',
  'America/Cayman',
  'America/Chicago',
  'America/Chihuahua',
  'America/Costa_Rica',
  'America/Creston',
  'America/Cuiaba',
  'America/Curacao',
  'America/Danmarkshavn',
  'America/Dawson',
  'America/Dawson_Creek',
  'America/Denver',
  'America/Detroit',
  'America/Dominica',
  'America/Edmonton',
  'America/Eirunepe',
  'America/El_Salvador',
  'America/Fort_Nelson',
  'America/Fortaleza',
  'America/Glace_Bay',
  'America/Godthab',
  'America/Goose_Bay',
  'America/Grand_Turk',
  'America/Grenada',
  'America/Guadeloupe',
  'America/Guatemala',
  'America/Guayaquil',
  'America/Guyana',
  'America/Halifax',
  'America/Havana',
  'America/Hermosillo',
  'America/Indiana/Indianapolis',
  'America/Indiana/Knox',
  'America/Indiana/Marengo',
  'America/Indiana/Petersburg',
  'America/Indiana/Tell_City',
  'America/Indiana/Vevay',
  'America/Indiana/Vincennes',
  'America/Indiana/Winamac',
  'America/Inuvik',
  'America/Iqaluit',
  'America/Jamaica',
  'America/Juneau',
  'America/Kentucky/Louisville',
  'America/Kentucky/Monticello',
  'America/Kralendijk',
  'America/La_Paz',
  'America/Lima',
  'America/Los_Angeles',
  'America/Lower_Princes',
  'America/Maceio',
  'America/Managua',
  'America/Manaus',
  'America/Marigot',
  'America/Martinique',
  'America/Matamoros',
  'America/Mazatlan',
  'America/Menominee',
  'America/Merida',
  'America/Metlakatla',
  'America/Mexico_City',
  'America/Miquelon',
  'America/Moncton',
  'America/Monterrey',
  'America/Montevideo',
  'America/Montserrat',
  'America/Nassau',
  'America/New_York',
  'America/Nipigon',
  'America/Nome',
  'America/Noronha',
  'America/North_Dakota/Beulah',
  'America/North_Dakota/Center',
  'America/North_Dakota/New_Salem',
  'America/Ojinaga',
  'America/Panama',
  'America/Pangnirtung',
  'America/Paramaribo',
  'America/Phoenix',
  'America/Port-au-Prince',
  'America/Port_of_Spain',
  'America/Porto_Velho',
  'America/Puerto_Rico',
  'America/Punta_Arenas',
  'America/Rainy_River',
  'America/Rankin_Inlet',
  'America/Recife',
  'America/Regina',
  'America/Resolute',
  'America/Rio_Branco',
  'America/Santarem',
  'America/Santiago',
  'America/Santo_Domingo',
  'America/Sao_Paulo',
  'America/Scoresbysund',
  'America/Sitka',
  'America/St_Barthelemy',
  'America/St_Johns',
  'America/St_Kitts',
  'America/St_Lucia',
  'America/St_Thomas',
  'America/St_Vincent',
  'America/Swift_Current',
  'America/Tegucigalpa',
  'America/Thule',
  'America/Thunder_Bay',
  'America/Tijuana',
  'America/Toronto',
  'America/Tortola',
  'America/Vancouver',
  'America/Whitehorse',
  'America/Winnipeg',
  'America/Yakutat',
  'America/Yellowknife',

  // Antarctica
  'Antarctica/Casey',
  'Antarctica/Davis',
  'Antarctica/DumontDUrville',
  'Antarctica/Macquarie',
  'Antarctica/Mawson',
  'Antarctica/McMurdo',
  'Antarctica/Palmer',
  'Antarctica/Rothera',
  'Antarctica/Syowa',
  'Antarctica/Troll',
  'Antarctica/Vostok',

  // Arctic
  'Arctic/Longyearbyen',

  // Asia
  'Asia/Aden',
  'Asia/Almaty',
  'Asia/Amman',
  'Asia/Anadyr',
  'Asia/Aqtau',
  'Asia/Aqtobe',
  'Asia/Ashgabat',
  'Asia/Atyrau',
  'Asia/Baghdad',
  'Asia/Bahrain',
  'Asia/Baku',
  'Asia/Bangkok',
  'Asia/Barnaul',
  'Asia/Beirut',
  'Asia/Bishkek',
  'Asia/Brunei',
  'Asia/Chita',
  'Asia/Choibalsan',
  'Asia/Colombo',
  'Asia/Damascus',
  'Asia/Dhaka',
  'Asia/Dili',
  'Asia/Dubai',
  'Asia/Dushanbe',
  'Asia/Famagusta',
  'Asia/Gaza',
  'Asia/Hebron',
  'Asia/Ho_Chi_Minh',
  'Asia/Hong_Kong',
  'Asia/Hovd',
  'Asia/Irkutsk',
  'Asia/Jakarta',
  'Asia/Jayapura',
  'Asia/Jerusalem',
  'Asia/Kabul',
  'Asia/Kamchatka',
  'Asia/Karachi',
  'Asia/Kathmandu',
  'Asia/Khandyga',
  'Asia/Kolkata',
  'Asia/Krasnoyarsk',
  'Asia/Kuala_Lumpur',
  'Asia/Kuching',
  'Asia/Kuwait',
  'Asia/Macau',
  'Asia/Magadan',
  'Asia/Makassar',
  'Asia/Manila',
  'Asia/Muscat',
  'Asia/Nicosia',
  'Asia/Novokuznetsk',
  'Asia/Novosibirsk',
  'Asia/Omsk',
  'Asia/Oral',
  'Asia/Phnom_Penh',
  'Asia/Pontianak',
  'Asia/Pyongyang',
  'Asia/Qatar',
  'Asia/Qostanay',
  'Asia/Qyzylorda',
  'Asia/Riyadh',
  'Asia/Sakhalin',
  'Asia/Samarkand',
  'Asia/Seoul',
  'Asia/Shanghai',
  'Asia/Singapore',
  'Asia/Srednekolymsk',
  'Asia/Taipei',
  'Asia/Tashkent',
  'Asia/Tbilisi',
  'Asia/Tehran',
  'Asia/Thimphu',
  'Asia/Tokyo',
  'Asia/Tomsk',
  'Asia/Ulaanbaatar',
  'Asia/Urumqi',
  'Asia/Ust-Nera',
  'Asia/Vientiane',
  'Asia/Vladivostok',
  'Asia/Yakutsk',
  'Asia/Yangon',
  'Asia/Yekaterinburg',
  'Asia/Yerevan',

  // Atlantic
  'Atlantic/Azores',
  'Atlantic/Bermuda',
  'Atlantic/Canary',
  'Atlantic/Cape_Verde',
  'Atlantic/Faroe',
  'Atlantic/Madeira',
  'Atlantic/Reykjavik',
  'Atlantic/South_Georgia',
  'Atlantic/St_Helena',
  'Atlantic/Stanley',

  // Australia
  'Australia/Adelaide',
  'Australia/Brisbane',
  'Australia/Broken_Hill',
  'Australia/Currie',
  'Australia/Darwin',
  'Australia/Eucla',
  'Australia/Hobart',
  'Australia/Lindeman',
  'Australia/Lord_Howe',
  'Australia/Melbourne',
  'Australia/Perth',
  'Australia/Sydney',

  // Europe
  'Europe/Amsterdam',
  'Europe/Andorra',
  'Europe/Astrakhan',
  'Europe/Athens',
  'Europe/Belgrade',
  'Europe/Berlin',
  'Europe/Bratislava',
  'Europe/Brussels',
  'Europe/Bucharest',
  'Europe/Budapest',
  'Europe/Busingen',
  'Europe/Chisinau',
  'Europe/Copenhagen',
  'Europe/Dublin',
  'Europe/Gibraltar',
  'Europe/Guernsey',
  'Europe/Helsinki',
  'Europe/Isle_of_Man',
  'Europe/Istanbul',
  'Europe/Jersey',
  'Europe/Kaliningrad',
  'Europe/Kiev',
  'Europe/Kirov',
  'Europe/Lisbon',
  'Europe/Ljubljana',
  'Europe/London',
  'Europe/Luxembourg',
  'Europe/Madrid',
  'Europe/Malta',
  'Europe/Mariehamn',
  'Europe/Minsk',
  'Europe/Monaco',
  'Europe/Moscow',
  'Europe/Oslo',
  'Europe/Paris',
  'Europe/Podgorica',
  'Europe/Prague',
  'Europe/Riga',
  'Europe/Rome',
  'Europe/Samara',
  'Europe/San_Marino',
  'Europe/Sarajevo',
  'Europe/Saratov',
  'Europe/Simferopol',
  'Europe/Skopje',
  'Europe/Sofia',
  'Europe/Stockholm',
  'Europe/Tallinn',
  'Europe/Tirane',
  'Europe/Ulyanovsk',
  'Europe/Uzhgorod',
  'Europe/Vaduz',
  'Europe/Vatican',
  'Europe/Vienna',
  'Europe/Vilnius',
  'Europe/Volgograd',
  'Europe/Warsaw',
  'Europe/Zagreb',
  'Europe/Zaporozhye',
  'Europe/Zurich',

  // Indian
  'Indian/Antananarivo',
  'Indian/Chagos',
  'Indian/Christmas',
  'Indian/Cocos',
  'Indian/Comoro',
  'Indian/Kerguelen',
  'Indian/Mahe',
  'Indian/Maldives',
  'Indian/Mauritius',
  'Indian/Mayotte',
  'Indian/Reunion',

  // Pacific
  'Pacific/Apia',
  'Pacific/Auckland',
  'Pacific/Bougainville',
  'Pacific/Chatham',
  'Pacific/Chuuk',
  'Pacific/Easter',
  'Pacific/Efate',
  'Pacific/Enderbury',
  'Pacific/Fakaofo',
  'Pacific/Fiji',
  'Pacific/Funafuti',
  'Pacific/Galapagos',
  'Pacific/Gambier',
  'Pacific/Guadalcanal',
  'Pacific/Guam',
  'Pacific/Honolulu',
  'Pacific/Johnston',
  'Pacific/Kiritimati',
  'Pacific/Kosrae',
  'Pacific/Kwajalein',
  'Pacific/Majuro',
  'Pacific/Marquesas',
  'Pacific/Midway',
  'Pacific/Nauru',
  'Pacific/Niue',
  'Pacific/Norfolk',
  'Pacific/Noumea',
  'Pacific/Pago_Pago',
  'Pacific/Palau',
  'Pacific/Pitcairn',
  'Pacific/Pohnpei',
  'Pacific/Port_Moresby',
  'Pacific/Rarotonga',
  'Pacific/Saipan',
  'Pacific/Tahiti',
  'Pacific/Tarawa',
  'Pacific/Tongatapu',
  'Pacific/Wake',
  'Pacific/Wallis',

  // UTC
  'UTC',
];

export default function LocationForm({ location, onClose, onSuccess }: LocationFormProps) {
  const isEditing = !!location;
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const { t } = useFormTranslation();
  const { t: tManagement, currentLanguage } = useManagementTranslation();

  // Custom translations for location form
  const formTranslations = {
    ar: {
      createLocation: "إنشاء موقع",
      editLocation: "تعديل الموقع",
      addNewLocationDescription: "أضف موقع خدمة جديد",
      updateLocationDescription: "تحديث تفاصيل الموقع",
      basicInformation: "المعلومات الأساسية",
      addressInformation: "معلومات العنوان",
      contactInformation: "معلومات الاتصال",
      amenitiesAccessibility: "المرافق وإمكانية الوصول",
      coordinates: "الإحداثيات",
      openingHours: "ساعات العمل",
      locationName: "اسم الموقع",
      shortName: "الاسم المختصر",
      streetAddress: "عنوان الشارع",
      city: "المدينة",
      country: "البلد",
      postalCode: "الرمز البريدي",
      floorSuite: "الطابق/الجناح",
      timezone: "المنطقة الزمنية",
      mobilePhone: "الهاتف المحمول",
      fax: "الفاكس",
      latitude: "خط العرض",
      longitude: "خط الطول",
      dayOfWeek: "يوم الأسبوع",
      enterName: "أدخل اسم الموقع",
      enterShortName: "اسم مختصر اختياري",
      enterAddress: "أدخل عنوان الشارع",
      enterCity: "أدخل المدينة",
      enterCountry: "أدخل البلد",
      enterPostalCode: "أدخل الرمز البريدي",
      floorNumber: "رقم الطابق أو الجناح",
      enterMobile: "أدخل رقم الهاتف المحمول",
      enterFax: "أدخل رقم الفاكس",
      latitudeExample: "مثال: 40.7128",
      longitudeExample: "مثال: -74.0060",
      useMyTimezone: "استخدم منطقتي الزمنية",
      selectTimezone: "اختر المنطقة الزمنية",
      standardHours: "ساعات عمل قياسية",
      addDay: "إضافة يوم",
      useCurrentLocation: "استخدم موقعي الحالي",
      gettingLocation: "جاري الحصول على الموقع...",
      createLocationBtn: "إنشاء موقع",
      updateLocationBtn: "تحديث الموقع",
      creating: "جاري الإنشاء...",
      updating: "جاري التحديث...",
      cancel: "إلغاء",
      hideMobile: "إخفاء رقم الهاتف المحمول عن العملاء",
      hideMobileDesc: "لن يتم عرض رقم الهاتف المحمول علناً",
      parkingAvailable: "موقف سيارات متاح",
      parkingDesc: "موقف سيارات في الموقع للعملاء",
      elevatorAccess: "مصعد متاح",
      elevatorDesc: "مصعد متاح في المبنى",
      handicapAccessible: "مناسب لذوي الاحتياجات الخاصة",
      handicapDesc: "مدخل مناسب للكراسي المتحركة",
      coordinatesHelp: "الإحداثيات تساعد العملاء في العثور على موقعك بسهولة أكبر على الخرائط",
      noOpeningHours: "لم يتم تحديد ساعات عمل. انقر على \"إضافة يوم\" للبدء.",
      active: "نشط",
      timeSlots: "فترات الوقت",
      addSlot: "إضافة فترة",
      from: "من",
      to: "إلى",
      noTimeSlots: "لا توجد فترات وقت. انقر على \"إضافة فترة\" لإضافة ساعات العمل.",
      monday: "الاثنين",
      tuesday: "الثلاثاء",
      wednesday: "الأربعاء",
      thursday: "الخميس",
      friday: "الجمعة",
      saturday: "السبت",
      sunday: "الأحد"
    },
    en: {
      createLocation: "Create Location",
      editLocation: "Edit Location",
      addNewLocationDescription: "Add a new service location",
      updateLocationDescription: "Update location details",
      basicInformation: "Basic Information",
      addressInformation: "Address Information",
      contactInformation: "Contact Information",
      amenitiesAccessibility: "Amenities & Accessibility",
      coordinates: "Coordinates",
      openingHours: "Opening Hours",
      locationName: "Location Name",
      shortName: "Short Name",
      streetAddress: "Street Address",
      city: "City",
      country: "Country",
      postalCode: "Postal Code",
      floorSuite: "Floor/Suite",
      timezone: "Timezone",
      mobilePhone: "Mobile Phone",
      fax: "Fax",
      latitude: "Latitude",
      longitude: "Longitude",
      dayOfWeek: "Day of Week",
      enterName: "Enter location name",
      enterShortName: "Optional short name",
      enterAddress: "Enter street address",
      enterCity: "Enter city",
      enterCountry: "Enter country",
      enterPostalCode: "Enter postal code",
      floorNumber: "Floor or suite number",
      enterMobile: "Enter mobile number",
      enterFax: "Enter fax number",
      latitudeExample: "e.g., 40.7128",
      longitudeExample: "e.g., -74.0060",
      useMyTimezone: "Use my timezone",
      selectTimezone: "Select timezone",
      standardHours: "Standard Hours",
      addDay: "Add Day",
      useCurrentLocation: "Use my current location",
      gettingLocation: "Getting location...",
      createLocationBtn: "Create Location",
      updateLocationBtn: "Update Location",
      creating: "Creating...",
      updating: "Updating...",
      cancel: "Cancel",
      hideMobile: "Hide mobile number from customers",
      hideMobileDesc: "Mobile number will not be displayed publicly",
      parkingAvailable: "Parking Available",
      parkingDesc: "On-site parking for customers",
      elevatorAccess: "Elevator Access",
      elevatorDesc: "Elevator available in building",
      handicapAccessible: "Handicap Accessible",
      handicapDesc: "Wheelchair accessible entrance",
      coordinatesHelp: "Coordinates help customers find your location more easily on maps",
      noOpeningHours: "No opening hours set. Click \"Add Day\" to get started.",
      active: "Active",
      timeSlots: "Time Slots",
      addSlot: "Add Slot",
      from: "From",
      to: "To",
      noTimeSlots: "No time slots. Click \"Add Slot\" to add opening hours.",
      monday: "Monday",
      tuesday: "Tuesday",
      wednesday: "Wednesday",
      thursday: "Thursday",
      friday: "Friday",
      saturday: "Saturday",
      sunday: "Sunday"
    },
    fr: {
      createLocation: "Créer un lieu",
      editLocation: "Modifier le lieu",
      addNewLocationDescription: "Ajouter un nouveau lieu de service",
      updateLocationDescription: "Mettre à jour les détails du lieu",
      basicInformation: "Informations de base",
      addressInformation: "Informations d'adresse",
      contactInformation: "Informations de contact",
      amenitiesAccessibility: "Équipements et accessibilité",
      coordinates: "Coordonnées",
      openingHours: "Heures d'ouverture",
      locationName: "Nom du lieu",
      shortName: "Nom court",
      streetAddress: "Adresse de rue",
      city: "Ville",
      country: "Pays",
      postalCode: "Code postal",
      floorSuite: "Étage/Suite",
      timezone: "Fuseau horaire",
      mobilePhone: "Téléphone mobile",
      fax: "Fax",
      latitude: "Latitude",
      longitude: "Longitude",
      dayOfWeek: "Jour de la semaine",
      enterName: "Entrez le nom du lieu",
      enterShortName: "Nom court optionnel",
      enterAddress: "Entrez l'adresse de rue",
      enterCity: "Entrez la ville",
      enterCountry: "Entrez le pays",
      enterPostalCode: "Entrez le code postal",
      floorNumber: "Numéro d'étage ou de suite",
      enterMobile: "Entrez le numéro de mobile",
      enterFax: "Entrez le numéro de fax",
      latitudeExample: "ex: 40.7128",
      longitudeExample: "ex: -74.0060",
      useMyTimezone: "Utiliser mon fuseau horaire",
      selectTimezone: "Sélectionner le fuseau horaire",
      standardHours: "Heures standard",
      addDay: "Ajouter un jour",
      useCurrentLocation: "Utiliser ma position actuelle",
      gettingLocation: "Obtention de la position...",
      createLocationBtn: "Créer un lieu",
      updateLocationBtn: "Mettre à jour le lieu",
      creating: "Création...",
      updating: "Mise à jour...",
      cancel: "Annuler",
      hideMobile: "Masquer le numéro de mobile aux clients",
      hideMobileDesc: "Le numéro de mobile ne sera pas affiché publiquement",
      parkingAvailable: "Parking disponible",
      parkingDesc: "Parking sur site pour les clients",
      elevatorAccess: "Accès par ascenseur",
      elevatorDesc: "Ascenseur disponible dans le bâtiment",
      handicapAccessible: "Accessible aux handicapés",
      handicapDesc: "Entrée accessible en fauteuil roulant",
      coordinatesHelp: "Les coordonnées aident les clients à trouver votre lieu plus facilement sur les cartes",
      noOpeningHours: "Aucune heure d'ouverture définie. Cliquez sur \"Ajouter un jour\" pour commencer.",
      active: "Actif",
      timeSlots: "Créneaux horaires",
      addSlot: "Ajouter un créneau",
      from: "De",
      to: "À",
      noTimeSlots: "Aucun créneau horaire. Cliquez sur \"Ajouter un créneau\" pour ajouter des heures d'ouverture.",
      monday: "Lundi",
      tuesday: "Mardi",
      wednesday: "Mercredi",
      thursday: "Jeudi",
      friday: "Vendredi",
      saturday: "Samedi",
      sunday: "Dimanche"
    }
  };

  const currentLang = currentLanguage as keyof typeof formTranslations;
  const ft = (key: keyof typeof formTranslations.ar) =>
    formTranslations[currentLang]?.[key] || formTranslations.en[key] || key;



  // Validation schema using translations
  const locationSchema = z.object({
    name: z.string().min(1, t('validation.required')),
    shortName: z.string().max(50, t('validation.maxLength', { max: 50 })).optional(),
    address: z.string().max(500, t('validation.maxLength', { max: 500 })).optional(),
    city: z.string().max(100, t('validation.maxLength', { max: 100 })).optional(),
    mobile: z.string().min(1, t('validation.required')),
    isMobileHidden: z.boolean().default(false),
    fax: z.string().max(50, t('validation.maxLength', { max: 50 })).optional(),
    floor: z.string().max(50, t('validation.maxLength', { max: 50 })).optional(),
    parking: z.boolean().default(false),
    elevator: z.boolean().default(false),
    handicapAccess: z.boolean().default(false),
    timezone: z.string().max(50, t('validation.maxLength', { max: 50 })).optional(),
    country: z.string().max(100, t('validation.maxLength', { max: 100 })).optional(),
    postalCode: z.string().max(20, t('validation.maxLength', { max: 20 })).optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    openingHours: z.array(z.any()).optional(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
  } = useForm<LocationFormData>({
    resolver: zodResolver(locationSchema),
    defaultValues: {
      name: '',
      shortName: '',
      address: '',
      city: '',
      mobile: '',
      isMobileHidden: false,
      fax: '',
      floor: '',
      parking: false,
      elevator: false,
      handicapAccess: false,
      timezone: '',
      country: 'Algeria',
      postalCode: '',
      latitude: 0,
      longitude: 0,
      openingHours: [],
    },
  });



  // Auto-fill timezone with user's timezone
  const handleAutoFillTimezone = () => {
    const userTimezone = getUserTimezone();
    setValue('timezone', userTimezone);
  };

  // Auto-fill location coordinates
  const handleAutoFillLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setValue('latitude', latitude);
        setValue('longitude', longitude);
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        let errorMessage = 'Unable to get your location. ';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Please allow location access and try again.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
          default:
            errorMessage += 'An unknown error occurred.';
            break;
        }
        alert(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };



  // Populate form when editing or auto-fill timezone for new locations
  useEffect(() => {
    if (location) {
      reset({
        name: location.name,
        shortName: location.shortName || '',
        address: location.address || '',
        city: location.city || '',
        mobile: location.mobile || '',
        isMobileHidden: location.isMobileHidden,
        fax: location.fax || '',
        floor: location.floor || '',
        parking: location.parking,
        elevator: location.elevator,
        handicapAccess: location.handicapAccess,
        timezone: location.timezone || '',
        country: location.country || 'Algeria',
        postalCode: location.postalCode || '',
        latitude: location.latitude,
        longitude: location.longitude,
        openingHours: location.openingHours?.map(opening => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours?.map(hour => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo,
          })) || [],
        })) || [],
      });
    } else {
      // Auto-fill timezone for new locations
      const userTimezone = getUserTimezone();
      setValue('timezone', userTimezone);
    }
  }, [location, reset, setValue]);

  const onSubmit = async (data: LocationFormData) => {
    try {
      const locationData: LocationCreateRequest = {
        name: data.name,
        shortName: data.shortName || undefined,
        address: data.address || undefined,
        city: data.city || undefined,
        mobile: data.mobile,
        isMobileHidden: data.isMobileHidden,
        fax: data.fax || undefined,
        floor: data.floor || undefined,
        parking: data.parking,
        elevator: data.elevator,
        handicapAccess: data.handicapAccess,
        timezone: data.timezone || undefined,
        // Flat address structure as per API
        country: data.country || 'Algeria',
        postalCode: data.postalCode || undefined,
        latitude: data.latitude,
        longitude: data.longitude,
        // Opening hours
        openingHours: data.openingHours && data.openingHours.length > 0 ? data.openingHours : undefined,
      };

      if (isEditing && location) {
        await updateLocationMutation.mutateAsync({
          id: location.id,
          data: locationData,
        });
      } else {
        await createLocationMutation.mutateAsync(locationData);
      }
      
      onSuccess();
    } catch (error) {
      // Error handled by mutations
    }
  };

  const isLoading = createLocationMutation.isPending || updateLocationMutation.isPending;
  const error = createLocationMutation.error || updateLocationMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? ft('editLocation') : ft('createLocation')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? ft('updateLocationDescription') : ft('addNewLocationDescription')}
          </p>
        </div>
      </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
            {/* Basic Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {ft('basicInformation')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    {ft('locationName')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('name')}
                    placeholder={ft('enterName')}
                    disabled={isLoading}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>{ft('shortName')}</Label>
                  <Input
                    {...register('shortName')}
                    placeholder={ft('enterShortName')}
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {ft('addressInformation')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label>{ft('streetAddress')}</Label>
                  <Input
                    {...register('address')}
                    placeholder={ft('enterAddress')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>{ft('city')}</Label>
                  <Input
                    {...register('city')}
                    placeholder={ft('enterCity')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>{ft('country')}</Label>
                  <Input
                    {...register('country')}
                    placeholder={ft('enterCountry')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>{ft('postalCode')}</Label>
                  <Input
                    {...register('postalCode')}
                    placeholder={ft('enterPostalCode')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>{ft('floorSuite')}</Label>
                  <Input
                    {...register('floor')}
                    placeholder={ft('floorNumber')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>{ft('timezone')}</Label>
                    <button
                      type="button"
                      onClick={handleAutoFillTimezone}
                      disabled={isLoading}
                      className="text-xs text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 font-medium"
                    >
                      {ft('useMyTimezone')}
                    </button>
                  </div>
                  <select
                    {...register('timezone')}
                    disabled={isLoading}
                    className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                  >
                    <option value="">{ft('selectTimezone')}</option>
                    {timezones.map((tz) => (
                      <option key={tz} value={tz}>
                        {tz}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {ft('contactInformation')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    {ft('mobilePhone')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('mobile')}
                    placeholder={ft('enterMobile')}
                    disabled={isLoading}
                  />
                  {errors.mobile && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.mobile.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>{ft('fax')}</Label>
                  <Input
                    {...register('fax')}
                    placeholder={ft('enterFax')}
                    disabled={isLoading}
                  />
                </div>

                <div className="md:col-span-2">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={watch('isMobileHidden')}
                      onChange={(checked) => setValue('isMobileHidden', checked)}
                      disabled={isLoading}
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {ft('hideMobile')}
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {ft('hideMobileDesc')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {ft('amenitiesAccessibility')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('parking')}
                    onChange={(checked) => setValue('parking', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('parkingAvailable')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('parkingDesc')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('elevator')}
                    onChange={(checked) => setValue('elevator', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('elevatorAccess')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('elevatorDesc')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('handicapAccess')}
                    onChange={(checked) => setValue('handicapAccess', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {ft('handicapAccessible')}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {ft('handicapDesc')}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Coordinates */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {ft('coordinates')}
              </h3>
              <div className="flex flex-col md:flex-row gap-6 items-end">
                <div className="flex-1">
                  <Label>
                    {ft('latitude')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('latitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder={ft('latitudeExample')}
                    disabled={isLoading}
                  />
                  {errors.latitude && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.latitude.message}
                    </p>
                  )}
                </div>

                <div className="flex-1">
                  <Label>
                    {ft('longitude')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('longitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder={ft('longitudeExample')}
                    disabled={isLoading}
                  />
                  {errors.longitude && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.longitude.message}
                    </p>
                  )}
                </div>

                <div className="flex-shrink-0">
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleAutoFillLocation}
                    disabled={isLoading || isGettingLocation}
                    className="w-11 h-11 bg-brand-600 hover:bg-brand-700 text-white flex items-center justify-center rounded-lg"
                    title={isGettingLocation ? ft('gettingLocation') : ft('useCurrentLocation')}
                  >
                    <svg
                      className="w-6 h-6"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                  </Button>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {ft('coordinatesHelp')}
              </p>
            </div>

            {/* Opening Hours */}
            <OpeningHoursForm
              control={control}
              watch={watch}
              setValue={setValue}
              fieldName="openingHours"
              disabled={isLoading}
            />

            {error && (
              <ErrorDisplay
                error={error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {ft('cancel')}
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading
                  ? (isEditing ? ft('updating') : ft('creating'))
                  : (isEditing ? ft('updateLocationBtn') : ft('createLocationBtn'))
                }
              </Button>
            </div>
          </form>
    </div>
  );
}
