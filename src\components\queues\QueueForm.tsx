import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import OpeningHoursForm from '../forms/OpeningHoursForm';
import { useCreateQueue, useUpdateQueue } from '../../hooks/useQueues';
import { useLocations } from '../../hooks/useLocations';
import { useServices } from '../../hooks/useServices';
import { useAuth } from '../../context/AuthContext';
import { Queue, QueueCreateRequest } from '../../types/queue';

import { useManagementTranslation } from '../../hooks/useTranslation';

// Days of the week - will be translated in component

// Queue opening hours schema matching API
const queueOpeningHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Validation schema matching API documentation
const queueSchema = z.object({
  title: z.string().min(1, "Queue title is required"),
  sProvidingPlaceId: z.number().int().positive("Valid location is required"),
  isActive: z.boolean().default(true),
  serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned"),
  openingHours: z.array(queueOpeningHoursSchema).optional(),
});

type QueueFormData = z.infer<typeof queueSchema>;

interface QueueFormProps {
  queue?: Queue | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function QueueForm({ queue, onClose, onSuccess }: QueueFormProps) {
  const isEditing = !!queue;
  const { isAuthenticated, token } = useAuth();
  const createQueueMutation = useCreateQueue();
  const updateQueueMutation = useUpdateQueue();
  const { data: locations } = useLocations();
  const { data: services } = useServices();
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for queue form
  const formTranslations = {
    ar: {
      createQueue: "إنشاء قائمة انتظار",
      editQueue: "تعديل قائمة الانتظار",
      createNewQueueDescription: "إنشاء قائمة انتظار جديدة لإدارة أوقات انتظار العملاء",
      updateQueueDescription: "تحديث تفاصيل قائمة الانتظار",
      queueTitle: "عنوان قائمة الانتظار",
      location: "الموقع",
      services: "الخدمات",
      openingHours: "ساعات العمل",
      queueTitlePlaceholder: "أدخل عنوان قائمة الانتظار",
      selectLocation: "اختر موقعاً",
      standardHours: "ساعات عمل قياسية",
      addDay: "إضافة يوم",
      remove: "إزالة",
      addTimeSlot: "إضافة فترة زمنية",
      cancel: "إلغاء",
      createQueueBtn: "إنشاء قائمة انتظار",
      updateQueueBtn: "تحديث قائمة الانتظار",
      submitting: "جاري الإرسال...",
      queueIsActive: "قائمة الانتظار نشطة",
      active: "نشط",
      to: "إلى",
      noOpeningHours: "لم يتم تحديد ساعات عمل. انقر على \"إضافة يوم\" للبدء.",
      monday: "الاثنين",
      tuesday: "الثلاثاء",
      wednesday: "الأربعاء",
      thursday: "الخميس",
      friday: "الجمعة",
      saturday: "السبت",
      sunday: "الأحد"
    },
    en: {
      createQueue: "Create Queue",
      editQueue: "Edit Queue",
      createNewQueueDescription: "Create a new queue for managing customer wait times",
      updateQueueDescription: "Update queue details",
      queueTitle: "Queue Title",
      location: "Location",
      services: "Services",
      openingHours: "Opening Hours",
      queueTitlePlaceholder: "Enter queue title",
      selectLocation: "Select a location",
      standardHours: "Standard Hours",
      addDay: "Add Day",
      remove: "Remove",
      addTimeSlot: "Add Time Slot",
      cancel: "Cancel",
      createQueueBtn: "Create Queue",
      updateQueueBtn: "Update Queue",
      submitting: "Submitting...",
      queueIsActive: "Queue is active",
      active: "Active",
      to: "to",
      noOpeningHours: "No opening hours set. Click \"Add Day\" to get started.",
      monday: "Monday",
      tuesday: "Tuesday",
      wednesday: "Wednesday",
      thursday: "Thursday",
      friday: "Friday",
      saturday: "Saturday",
      sunday: "Sunday"
    },
    fr: {
      createQueue: "Créer une file d'attente",
      editQueue: "Modifier la file d'attente",
      createNewQueueDescription: "Créer une nouvelle file d'attente pour gérer les temps d'attente des clients",
      updateQueueDescription: "Mettre à jour les détails de la file d'attente",
      queueTitle: "Titre de la file d'attente",
      location: "Emplacement",
      services: "Services",
      openingHours: "Heures d'ouverture",
      queueTitlePlaceholder: "Entrez le titre de la file d'attente",
      selectLocation: "Sélectionner un emplacement",
      standardHours: "Heures standard",
      addDay: "Ajouter un jour",
      remove: "Supprimer",
      addTimeSlot: "Ajouter un créneau",
      cancel: "Annuler",
      createQueueBtn: "Créer une file d'attente",
      updateQueueBtn: "Mettre à jour la file d'attente",
      submitting: "Envoi en cours...",
      queueIsActive: "File d'attente active",
      active: "Actif",
      to: "à",
      noOpeningHours: "Aucune heure d'ouverture définie. Cliquez sur \"Ajouter un jour\" pour commencer.",
      monday: "Lundi",
      tuesday: "Mardi",
      wednesday: "Mercredi",
      thursday: "Jeudi",
      friday: "Vendredi",
      saturday: "Samedi",
      sunday: "Dimanche"
    }
  };

  const currentLang = currentLanguage as keyof typeof formTranslations;
  const ft = (key: keyof typeof formTranslations.ar) =>
    formTranslations[currentLang]?.[key] || formTranslations.en[key] || key;



  const isLoading = createQueueMutation.isPending || updateQueueMutation.isPending;
  const error = createQueueMutation.error || updateQueueMutation.error;

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
    getValues,
  } = useForm<QueueFormData>({
    resolver: zodResolver(queueSchema),
    defaultValues: {
      title: '',
      sProvidingPlaceId: 0,
      isActive: true,
      serviceIds: [],
      openingHours: [],
    },
  });



  // Populate form when editing
  useEffect(() => {
    if (queue) {
      reset({
        title: queue.title,
        sProvidingPlaceId: queue.sProvidingPlaceId,
        isActive: queue.isActive,
        serviceIds: queue.services?.map(s => s.id) || [],
        openingHours: queue.openingHours?.map(opening => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours?.map(hour => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo,
          })) || [],
        })) || [],
      });
    }
  }, [queue, reset]);

  const onSubmit = async (data: QueueFormData) => {
    console.log('🚀 Queue form submission started:', data);

    // Check authentication before proceeding
    if (!isAuthenticated || !token) {
      console.error('❌ User not authenticated');
      alert('Session expired. Please log in again.');
      return;
    }

    console.log('✅ User authenticated, proceeding with submission');

    try {
      const queueData: QueueCreateRequest = {
        title: data.title,
        sProvidingPlaceId: data.sProvidingPlaceId,
        isActive: data.isActive,
        serviceIds: data.serviceIds,
        openingHours: data.openingHours && data.openingHours.length > 0 ? data.openingHours : undefined,
      };

      console.log('📝 Queue data prepared:', queueData);

      if (isEditing && queue) {
        console.log('✏️ Updating existing queue:', queue.id);
        await updateQueueMutation.mutateAsync({
          id: queue.id,
          ...queueData,
        });
      } else {
        console.log('➕ Creating new queue');
        await createQueueMutation.mutateAsync(queueData);
      }

      console.log('✅ Queue operation completed successfully');
      onSuccess();
    } catch (error) {
      console.error('❌ Queue form submission error:', error);
      // Error handled by mutation
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? ft('editQueue') : ft('createQueue')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? ft('updateQueueDescription') : ft('createNewQueueDescription')}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Queue Title */}
        <div>
          <Label>
            {ft('queueTitle')} <span className="text-red-500">*</span>
          </Label>
          <Input
            {...register('title')}
            placeholder={ft('queueTitlePlaceholder')}
            disabled={isLoading}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.title.message}
            </p>
          )}
        </div>

        {/* Location Selection */}
        <div>
          <Label>
            {ft('location')} <span className="text-red-500">*</span>
          </Label>
          <select
            {...register('sProvidingPlaceId', { valueAsNumber: true })}
            disabled={isLoading}
            className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
          >
            <option value={0}>{ft('selectLocation')}</option>
            {locations?.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
          {errors.sProvidingPlaceId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.sProvidingPlaceId.message}
            </p>
          )}
        </div>

        {/* Services Selection */}
        <div>
          <Label>
            {ft('services')} <span className="text-red-500">*</span>
          </Label>
          <Controller
            name="serviceIds"
            control={control}
            render={({ field }) => (
              <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-lg p-3">
                {services?.map((service) => (
                  <label key={service.id} className="flex items-center">
                    <Checkbox
                      checked={field.value?.includes(service.id) || false}
                      onChange={(checked) => {
                        const currentValue = field.value || [];
                        if (checked) {
                          field.onChange([...currentValue, service.id]);
                        } else {
                          field.onChange(currentValue.filter((id: number) => id !== service.id));
                        }
                      }}
                      disabled={isLoading}
                    />
                    <span className="ms-2 text-sm text-gray-900 dark:text-white">
                      {service.title}
                    </span>
                  </label>
                ))}
              </div>
            )}
          />
          {errors.serviceIds && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.serviceIds.message}
            </p>
          )}
        </div>

        {/* Queue Status */}
        <div className="flex items-center">
          <Checkbox
            {...register('isActive')}
            id="isActive"
            disabled={isLoading}
          />
          <label htmlFor="isActive" className="ms-2 block text-sm text-gray-900 dark:text-white">
            {ft('queueIsActive')}
          </label>
        </div>

        {/* Opening Hours */}
        <OpeningHoursForm
          control={control}
          watch={watch}
          setValue={setValue}
          fieldName="openingHours"
          disabled={isLoading}
        />

        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            {ft('cancel')}
          </Button>
          <Button
            type="submit"
            disabled={createQueueMutation.isPending || updateQueueMutation.isPending}
          >
            {(createQueueMutation.isPending || updateQueueMutation.isPending)
              ? ft('submitting')
              : (isEditing ? ft('updateQueueBtn') : ft('createQueueBtn'))
            }
          </Button>
        </div>
      </form>
    </div>
  );
}
