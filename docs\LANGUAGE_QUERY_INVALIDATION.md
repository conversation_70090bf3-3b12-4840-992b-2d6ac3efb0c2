# Language Query Invalidation System

## Overview

This document describes the implementation of automatic query invalidation when users switch languages in the Dalti Provider Dashboard. This ensures that all data is refetched with the new language preference, providing a seamless multilingual experience.

## Problem Statement

The application supports multiple languages (English, French, Arabic) and has two types of translatable content:

1. **Static UI translations** - Handled by i18next (buttons, labels, form fields)
2. **Dynamic API content** - Data from the backend that may be localized (service names, location names, appointment statuses, notifications, etc.)

When users switch languages, static translations update immediately, but cached API data remains in the old language until manually refreshed or cache expires.

## Solution Architecture

### 1. Provider Hierarchy Setup

The provider hierarchy in `main.tsx` has been adjusted to ensure `LanguageProvider` has access to the `QueryClient`:

```typescript
// In src/main.tsx
<I18nextProvider i18n={i18n}>
  <QueryClientProvider client={queryClient}>
    <LanguageProvider>
      <ThemeProvider>
        <AuthProvider>
          {/* App components */}
        </AuthProvider>
      </ThemeProvider>
    </LanguageProvider>
  </QueryClientProvider>
</I18nextProvider>
```

### 2. Core Implementation in LanguageContext

The `LanguageContext` has been enhanced to automatically invalidate all React Query caches when language changes:

```typescript
// In src/context/LanguageContext.tsx
const changeLanguage = async (languageCode: LanguageCode) => {
  // ... existing language change logic ...
  
  // Invalidate all queries to refetch data with new language
  console.log('🔄 Invalidating all queries due to language change:', languageCode);
  queryClient.invalidateQueries();
  
  // Dispatch custom event for other components
  window.dispatchEvent(new CustomEvent('languageChanged', {
    detail: { language: languageCode, direction: rtlLanguages.includes(languageCode) ? 'rtl' : 'ltr' }
  }));
};
```

### 3. Application-Level Integration

The `AppLayout` component uses the auto-invalidation hook to ensure all protected pages benefit from automatic query invalidation:

```typescript
// In src/layout/AppLayout.tsx
useAutoLanguageInvalidation({
  invalidateAll: true, // Invalidate all queries when language changes
  refetchActive: false, // Don't force immediate refetch, let components handle it naturally
});
```

### 4. Custom Hooks for Advanced Control

#### `useLanguageQueryInvalidation`

Provides manual control over query invalidation:

```typescript
const {
  invalidateAllQueries,
  invalidateTranslatableQueries,
  clearAllQueries,
  refetchAllQueries,
} = useLanguageQueryInvalidation();
```

#### `useAutoLanguageInvalidation`

Automatically handles invalidation based on configuration:

```typescript
useAutoLanguageInvalidation({
  invalidateAll: true,        // Invalidate all queries
  invalidatePatterns: [],     // Or specific patterns
  clearCache: false,          // Clear vs invalidate
  refetchActive: false,       // Force immediate refetch
});
```

#### `useLanguageChangeListener`

For components needing custom language change handling:

```typescript
useLanguageChangeListener((language, direction) => {
  // Custom logic when language changes
  console.log(`Language changed to ${language} (${direction})`);
});
```

## Implementation Details

### Query Invalidation Strategy

1. **Global Invalidation**: When language changes, all queries are invalidated using `queryClient.invalidateQueries()`
2. **Natural Refetching**: Components refetch data naturally when they re-render and detect stale queries
3. **Preserved Loading States**: React Query's built-in loading states provide smooth UX during refetching
4. **Error Handling**: Existing error handling in hooks continues to work

### Performance Considerations

1. **Stale-While-Revalidate**: React Query shows cached data while refetching in background
2. **Selective Invalidation**: Option to invalidate only specific query patterns if needed
3. **Debounced Changes**: Language changes are typically user-initiated and infrequent
4. **Background Refetching**: Queries refetch in background without blocking UI

### Backward Compatibility

- Existing components continue to work without changes
- Static translations (i18next) work as before
- No breaking changes to existing query hooks
- Optional integration for components needing custom behavior

## Usage Examples

### Basic Usage (Automatic)

Most components get automatic invalidation without any changes:

```typescript
// This component automatically gets fresh data when language changes
const MyComponent = () => {
  const { data: services } = useServices();
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('services.title')}</h1> {/* Static translation */}
      {services?.map(service => (
        <div key={service.id}>{service.name}</div> /* API data - auto-refreshed */
      ))}
    </div>
  );
};
```

### Custom Invalidation

For components needing specific control:

```typescript
const AdvancedComponent = () => {
  const { invalidateTranslatableQueries } = useLanguageQueryInvalidation();
  
  // Only invalidate queries that contain translatable content
  useAutoLanguageInvalidation({
    invalidatePatterns: ['services', 'locations', 'notifications']
  });
  
  return <div>...</div>;
};
```

### Manual Handling

For components needing custom language change logic:

```typescript
const CustomComponent = () => {
  useLanguageChangeListener((language, direction) => {
    // Custom logic when language changes
    if (language === 'ar') {
      // Special handling for Arabic
      document.body.classList.add('rtl-mode');
    }
  });
  
  return <div>...</div>;
};
```

## Testing

### Test Page

A comprehensive test page is available at `/test-language-invalidation` that:

- Shows current language and query status
- Provides buttons to change language
- Displays query timestamps to verify refetching
- Offers manual invalidation testing
- Shows real-time test results

### Manual Testing

1. Navigate to any page with API data (services, locations, appointments)
2. Switch language using the language switcher
3. Observe that data is refetched (loading indicators appear briefly)
4. Verify that any localized content updates appropriately

### Automated Testing

The implementation includes console logging for debugging:

```typescript
// Language changes are logged
console.log('🔄 Invalidating all queries due to language change:', languageCode);

// Query invalidations are logged
console.log('🔄 Invalidating specific queries due to language change:', patterns);
```

## Configuration Options

### Global Configuration

In `LanguageContext`, the invalidation is configured to:
- Invalidate all queries by default
- Use React Query's built-in stale-while-revalidate behavior
- Maintain existing error handling and retry logic

### Component-Level Configuration

Components can opt for different strategies:

```typescript
// Invalidate everything (default)
useAutoLanguageInvalidation({ invalidateAll: true });

// Invalidate specific patterns only
useAutoLanguageInvalidation({ 
  invalidatePatterns: ['services', 'locations'] 
});

// Clear cache completely (more aggressive)
useAutoLanguageInvalidation({ clearCache: true });

// Force immediate refetch
useAutoLanguageInvalidation({ 
  invalidateAll: true, 
  refetchActive: true 
});
```

## Best Practices

1. **Use Default Behavior**: Most components should rely on the automatic invalidation
2. **Selective Invalidation**: Only use specific patterns if you have performance concerns
3. **Avoid Force Refetch**: Let React Query handle natural refetching for better UX
4. **Test Thoroughly**: Always test language switching with real API data
5. **Monitor Performance**: Watch for excessive API calls during language changes

## Troubleshooting

### Common Issues

1. **Data Not Updating**: Check if component is using React Query hooks correctly
2. **Multiple API Calls**: Verify that invalidation isn't being triggered multiple times
3. **Loading States**: Ensure components handle loading states during refetching
4. **Error Handling**: Verify that existing error handling still works

### Debug Tools

1. **Console Logs**: Check browser console for invalidation logs
2. **React Query DevTools**: Use to inspect query states and invalidations
3. **Test Page**: Use `/test-language-invalidation` for comprehensive testing
4. **Network Tab**: Monitor API calls during language changes

## Future Enhancements

1. **Selective Backend Calls**: Only refetch queries that actually contain translatable content
2. **Language-Aware Caching**: Cache data per language to avoid unnecessary API calls
3. **Progressive Enhancement**: Gradually migrate to more sophisticated invalidation strategies
4. **Performance Metrics**: Add monitoring for language change performance impact
