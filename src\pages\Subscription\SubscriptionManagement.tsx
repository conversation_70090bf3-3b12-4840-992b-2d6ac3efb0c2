import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { 
  useSubscriptionStatus, 
  useUsageStatistics, 
  useSubscriptionPlans 
} from "../../hooks/useSubscription";
import {
  SubscriptionPlansGrid,
  SubscriptionStatusWidget,
  UsageStatistics,
  CheckoutModal,
  CustomerPortalCard,
  UsedCreditsCard,
  SubscriptionUsageOverview,
  SubscriptionOverviewLayout
} from "../../components/subscription";
import { useUsedCredits } from "../../hooks/useUsedCredits";
import { useManagementTranslation } from "../../hooks/useTranslation";

export default function SubscriptionManagement() {
  const [activeTab, setActiveTab] = useState<'overview' | 'plans' | 'usage'>('overview');
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const { isOpen: isCheckoutOpen, openModal: openCheckout, closeModal: closeCheckout } = useModal();
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for subscription page
  const subscriptionTranslations = {
    ar: {
      title: "الاشتراك",
      management: "إدارة الاشتراك",
      description: "إدارة اشتراكك وعرض الاستخدام وترقية خطتك",
      upgradePlan: "ترقية الخطة",
      changePlan: "تغيير الخطة",
      overview: "نظرة عامة",
      plans: "الخطط",
      usage: "الاستخدام",
      failedToLoad: "فشل في تحميل بيانات الاشتراك",
      currentPlanDetails: "تفاصيل الخطة الحالية",
      usageInsights: "رؤى الاستخدام"
    },
    en: {
      title: "Subscription",
      management: "Subscription Management",
      description: "Manage your subscription, view usage, and upgrade your plan",
      upgradePlan: "Upgrade Plan",
      changePlan: "Change Plan",
      overview: "Overview",
      plans: "Plans",
      usage: "Usage",
      failedToLoad: "Failed to load subscription data",
      currentPlanDetails: "Current Plan Details",
      usageInsights: "Usage Insights"
    },
    fr: {
      title: "Abonnement",
      management: "Gestion des abonnements",
      description: "Gérez votre abonnement, consultez l'utilisation et mettez à niveau votre plan",
      upgradePlan: "Mettre à niveau le plan",
      changePlan: "Changer de plan",
      overview: "Aperçu",
      plans: "Plans",
      usage: "Utilisation",
      failedToLoad: "Échec du chargement des données d'abonnement",
      currentPlanDetails: "Détails du plan actuel",
      usageInsights: "Aperçus d'utilisation"
    }
  };

  const currentLang = currentLanguage as keyof typeof subscriptionTranslations;
  const st = (key: keyof typeof subscriptionTranslations.ar | string, params?: { amount?: number; plan?: string }) => {
    // Handle nested keys like 'overview.planName'
    if (key.includes('.')) {
      const [section, subKey] = key.split('.');
      if (section === 'overview') {
        const overviewKeys = {
          planName: currentLang === 'ar' ? 'اسم الخطة' : currentLang === 'fr' ? 'Nom du plan' : 'Plan Name',
          price: currentLang === 'ar' ? 'السعر' : currentLang === 'fr' ? 'Prix' : 'Price',
          credits: currentLang === 'ar' ? 'الرصيد' : currentLang === 'fr' ? 'Crédits' : 'Credits',
          queues: currentLang === 'ar' ? 'الطوابير' : currentLang === 'fr' ? 'Files d\'attente' : 'Queues',
          perMonth: currentLang === 'ar' ? '/شهر' : currentLang === 'fr' ? '/mois' : '/month',
          unlimited: currentLang === 'ar' ? 'غير محدود' : currentLang === 'fr' ? 'Illimité' : 'Unlimited',
          features: currentLang === 'ar' ? 'الميزات' : currentLang === 'fr' ? 'Fonctionnalités' : 'Features',
          creditsDisplay: currentLang === 'ar' ? '{amount} رصيد' : currentLang === 'fr' ? '{amount} Crédits' : '{amount} Credits'
        };
        let text = overviewKeys[subKey as keyof typeof overviewKeys] || subKey;
        if (params) {
          Object.entries(params).forEach(([param, value]) => {
            text = text.replace(`{${param}}`, value?.toString() || '');
          });
        }
        return text;
      }
    }
    return subscriptionTranslations[currentLang]?.[key as keyof typeof subscriptionTranslations.ar] || subscriptionTranslations.en[key as keyof typeof subscriptionTranslations.ar] || key;
  };

  const { data: statusData, isLoading: statusLoading, error: statusError } = useSubscriptionStatus();
  const { data: usageData, isLoading: usageLoading } = useUsageStatistics();
  const { data: plansData, isLoading: plansLoading } = useSubscriptionPlans();
  const {
    used,
    remaining,
    allocated,
    totalAvailable,
    totalQueues,
    usedQueues,
    completed,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod,
    isLoading: creditsLoading,
    error: creditsError,
    refreshData: refreshCreditsData
  } = useUsedCredits();

  const handleUpgrade = () => {
    setSelectedPlanId(''); // Clear any preselected plan
    openCheckout();
  };

  const handlePlanSelect = (planId: string) => {
    console.log('💳 Plan selected for checkout:', planId);
    setSelectedPlanId(planId);
    openCheckout();
  };

  const handleCloseCheckout = () => {
    setSelectedPlanId(''); // Clear selected plan when modal closes
    closeCheckout();
  };

  const getCurrentPlan = () => {
    if (!statusData?.data?.subscription || !plansData?.data?.plans) return null;
    return plansData.data.plans.find(plan => plan.id === statusData.data.subscription.planId);
  };

  const isLoading = statusLoading || usageLoading || plansLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (statusError) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={statusError}
          title={st('failedToLoad')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  const currentPlan = getCurrentPlan();
  const subscription = statusData?.data?.subscription;
  const usage = usageData?.data;

  return (
    <>
      <PageMeta
        title="Subscription Management | Provider Dashboard"
        description="Manage your subscription, billing, and usage"
      />
      <PageBreadcrumb pageTitle={st('title')} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {st('management')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {st('description')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            {subscription?.planId === 'free' ? (
              <Button onClick={handleUpgrade} size="sm">
                {st('upgradePlan')}
              </Button>
            ) : (
              <Button onClick={handleUpgrade} variant="outline" size="sm">
                {st('changePlan')}
              </Button>
            )}

          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: st('overview'), icon: '📊' },
              { id: 'plans', label: st('plans'), icon: '💳' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Overview Layout - Matches the design */}
              <SubscriptionOverviewLayout />


              {/* Current Plan Details */}
              {currentPlan && (
                <div className="lg:col-span-3">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {st('currentPlanDetails')}
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{st('overview.planName')}</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.name}
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{st('overview.price')}</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.price}
                          {currentPlan.isSubscription && currentPlan.id !== 'free' && (
                            <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">{st('overview.perMonth')}</span>
                          )}
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{st('overview.credits')}</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.effect.kind === 'credits'
                            ? st('overview.creditsDisplay', { amount: currentPlan.effect.amount })
                            : `${currentPlan.effect.amount}${st('overview.perMonth')}`
                          }
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{st('overview.queues')}</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.effect.queues || st('overview.unlimited')}
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{st('overview.features')}</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {currentPlan.features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Portal Access */}
              <div className="lg:col-span-3">
                <CustomerPortalCard />
              </div>
            </div>
          )}

          {activeTab === 'plans' && (
            <div>
              <SubscriptionPlansGrid
                showRecommended={true}
                onPlanSelect={handlePlanSelect}
              />
            </div>
          )}
        </div>
      </div>

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={handleCloseCheckout}
        preselectedPlanId={selectedPlanId}
        title={selectedPlanId ? "subscription.confirmSelection" : "subscription.choosePlan"}
        showPlanComparison={!selectedPlanId}
      />
    </>
  );
}
