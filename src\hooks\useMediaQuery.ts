import { useState, useEffect } from 'react';

/**
 * Hook for responsive design using media queries
 * @param query - CSS media query string
 * @returns boolean indicating if the media query matches
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    
    // Set initial value
    setMatches(media.matches);

    // Create event listener
    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add listener
    if (media.addEventListener) {
      media.addEventListener('change', listener);
    } else {
      // Fallback for older browsers
      media.addListener(listener);
    }

    // Cleanup
    return () => {
      if (media.removeEventListener) {
        media.removeEventListener('change', listener);
      } else {
        // Fallback for older browsers
        media.removeListener(listener);
      }
    };
  }, [query]);

  return matches;
};

/**
 * Hook for common responsive breakpoints
 */
export const useResponsive = () => {
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isDesktop = useMediaQuery('(min-width: 1024px)');
  const isLarge = useMediaQuery('(min-width: 1280px)');
  
  // Additional utility breakpoints
  const isSmallMobile = useMediaQuery('(max-width: 479px)');
  const isMediumMobile = useMediaQuery('(min-width: 480px) and (max-width: 767px)');
  const isSmallTablet = useMediaQuery('(min-width: 768px) and (max-width: 991px)');
  const isLargeTablet = useMediaQuery('(min-width: 992px) and (max-width: 1023px)');

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLarge,
    isSmallMobile,
    isMediumMobile,
    isSmallTablet,
    isLargeTablet,
    // Convenience properties
    isMobileOrTablet: isMobile || isTablet,
    isTabletOrDesktop: isTablet || isDesktop,
  };
};

/**
 * Hook for getting current screen size category
 */
export const useScreenSize = () => {
  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive();
  
  let size: 'mobile' | 'tablet' | 'desktop' | 'large' = 'desktop';
  
  if (isMobile) size = 'mobile';
  else if (isTablet) size = 'tablet';
  else if (isLarge) size = 'large';
  else size = 'desktop';
  
  return {
    size,
    isMobile,
    isTablet,
    isDesktop,
    isLarge,
  };
};
