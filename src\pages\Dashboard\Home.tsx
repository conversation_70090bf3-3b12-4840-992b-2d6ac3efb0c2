import { useEffect } from "react";
import PageMeta from "../../components/common/PageMeta";
import { ProfileCompletionExample } from "../../components/profile-completion";
import ActiveSessionWidget from "../../components/dashboard/ActiveSessionWidget";
import TodayAppointments from "../../components/dashboard/TodayAppointments";
import DashboardMetrics from "../../components/dashboard/DashboardMetrics";
import QuickActions from "../../components/provider/QuickActions";
import RecentAppointmentsSimple from "../../components/dashboard/RecentAppointmentsSimple";
import AppointmentAnalyticsChart from "../../components/dashboard/AppointmentAnalyticsChart";
import QueueStatsWidget from "../../components/dashboard/QueueStatsWidget";
import PendingAppointments from "../../components/dashboard/PendingAppointments";
import { useRealTimeFeatures } from "../../hooks/useRealTimeUpdates";
import { useDashboardRefresh } from "../../hooks/useDashboardRefresh";

import { useAuth } from "../../context/AuthContext";

export default function Home() {
  const { user } = useAuth();
  const { refreshData } = useRealTimeFeatures();
  useDashboardRefresh(); // This hook handles all the dashboard refresh logic

  // Set up periodic refresh for dashboard data as fallback
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 60000); // Refresh all data every minute as a fallback

    return () => clearInterval(interval);
  }, [refreshData]);

  return (
    <>
      <PageMeta
        title="Provider Dashboard | Manage Your Business"
        description="Provider dashboard with appointments, services, and business metrics"
      />



      {/* Profile Completion Card */}
      {user && (
        <ProfileCompletionExample
          userId={user.id}
          showDetails={true}
          className="mb-6"
        />
      )}

       {/* Quick Actions Row */}
      <div className="mb-6">
        <QuickActions />
      </div>

      {/* Dashboard Metrics Row */}
      <div className="mb-6">
        <DashboardMetrics />
      </div>

     

      <div className="grid grid-cols-12 gap-4 md:gap-6">
        {/* Priority 1: Essential Operational Widgets */}
        <div className="col-span-12 xl:col-span-4 space-y-6">
          <ActiveSessionWidget />
          <PendingAppointments />
          <TodayAppointments />
          {/* <QueueStatsWidget /> */}
        </div>

        {/* Priority 2: Appointment Analytics and Charts */}
        <div className="col-span-12 xl:col-span-8 space-y-6">
          <AppointmentAnalyticsChart />
          <RecentAppointmentsSimple />
        </div>
      </div>
    </>
  );
}
